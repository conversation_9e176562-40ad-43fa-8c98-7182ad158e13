{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-05-30T00:11:32.599626Z", "start_time": "2025-05-30T00:11:32.593645Z"}}, "source": "print(\"hello, world\")", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello, world\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-30T00:37:15.865222Z", "start_time": "2025-05-30T00:37:14.132218Z"}}, "cell_type": "code", "source": ["# main.py\n", "from fastapi import FastAPI\n", "\n", "app = FastAPI()\n", "\n", "@app.get(\"/\")\n", "def read_root():\n", "    return {\"message\": \"Hello FastAPI\"}\n", "\n", "@app.get(\"/items/{item_id}\")\n", "def read_item(item_id: int, q: str = None):\n", "    return {\"item_id\": item_id, \"query\": q}\n"], "id": "6f6ba98e3688efaa", "outputs": [], "execution_count": 2}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}