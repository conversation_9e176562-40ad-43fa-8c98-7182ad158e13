{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习-降维"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sb\n", "from scipy.io import loadmat # 从scipy.io模块导入loadmat函数，用于加载MATLAB格式的文件"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 奇异值分解（Singular Value Decomposition）"]}, {"attachments": {"1749109138730.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![1749109138730.png](attachment:1749109138730.png)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 生成一个随机矩阵\n", "np.random.seed(42)  \n", "#使用np.random.rand函数生成5行3列的二维数组（填空）\n", "A = pass\n", "\n", "# 计算SVD\n", "U, S, VT = np.linalg.svd(A, full_matrices=True)  \n", "# 构造奇异值矩阵\n", "# 创建一个与矩阵U的列数和矩阵VT的行数相同的零矩阵Sigma（填空）\n", "Sigma = pass\n", "\n", "# 将奇异值将对角矩阵S嵌入到Sigma的前len(S)行和前len(S)列中（填空）\n", "pass = np.diag(S)\n", "\n", "# 根据公式重构原矩阵A（填空）\n", "A_reconstructed = pass\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原矩阵A:\n", "[[0.37454012 0.95071431 0.73199394]\n", " [0.59865848 0.15601864 0.15599452]\n", " [0.05808361 0.86617615 0.60111501]\n", " [0.70807258 0.02058449 0.96990985]\n", " [0.83244264 0.21233911 0.18182497]]\n", "\n", "左奇异矩阵U:\n", "[[-0.5991048  -0.38620771 -0.12988737 -0.68883081 -0.02363108]\n", " [-0.25170251  0.32375656 -0.38389036  0.13776803 -0.81576694]\n", " [-0.4495347  -0.55516825  0.01152904  0.69900869  0.03099514]\n", " [-0.51180949  0.4814656   0.71001691  0.04057048  0.0217244 ]\n", " [-0.33717783  0.45387706 -0.57576083  0.12756552  0.57665694]]\n", "\n", "奇异值矩阵Sigma:\n", "[[1.99063285 0.         0.        ]\n", " [0.         1.0096001  0.        ]\n", " [0.         0.         0.57767497]\n", " [0.         0.         0.        ]\n", " [0.         0.         0.        ]]\n", "\n", "右奇异矩阵VT:\n", "[[-0.52458829 -0.54271957 -0.65594405]\n", " [ 0.72866708 -0.6846751  -0.01625695]\n", " [-0.44028559 -0.48649304  0.75463443]]\n", "\n", "重构的原矩阵A_reconstructed:\n", "[[0.37454012 0.95071431 0.73199394]\n", " [0.59865848 0.15601864 0.15599452]\n", " [0.05808361 0.86617615 0.60111501]\n", " [0.70807258 0.02058449 0.96990985]\n", " [0.83244264 0.21233911 0.18182497]]\n", "\n", "重构矩阵与原矩阵是否相同: True\n"]}], "source": ["# 打印结果\n", "print(\"原矩阵A:\")\n", "print(A)\n", "print(\"\\n左奇异矩阵U:\")\n", "print(U)\n", "print(\"\\n奇异值矩阵Sigma:\")\n", "print(Sigma)\n", "print(\"\\n右奇异矩阵VT:\")\n", "print(VT)\n", "print(\"\\n重构的原矩阵A_reconstructed:\")\n", "print(A_reconstructed)\n", "\n", "# 验证重构矩阵是否与原矩阵相同，使用NumPy库的allclose函数来检查两个矩阵是否接近相等\n", "print(\"\\n重构矩阵与原矩阵是否相同:\", np.allclose(A, A_reconstructed))  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Principal component analysis（主成分分析）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PCA是在数据集中找到“主成分”或最大方差方向的线性变换。 它可以用于降维。 在本练习中，我们首先负责实现PCA并将其应用于一个简单的二维数据集，以了解它是如何工作的。 我们从加载和可视化数据集开始。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>X1</th>\n", "      <th>X2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3.381563</td>\n", "      <td>3.389113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.527875</td>\n", "      <td>5.854178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2.655682</td>\n", "      <td>4.411995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2.765235</td>\n", "      <td>3.715414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.846560</td>\n", "      <td>4.175506</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         X1        X2\n", "0  3.381563  3.389113\n", "1  4.527875  5.854178\n", "2  2.655682  4.411995\n", "3  2.765235  3.715414\n", "4  2.846560  4.175506"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用pandas读取csv文件pcadata（填空）\n", "data = pass\n", "data.head()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["#将 data 转换为 NumPy 数组 X\n", "X = data.values"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "# 在二维坐标系中绘制了 X 的第一列（即第一个特征）和第二列（即第二个特征）的值（填空）\n", "ax.scatter(pass)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PCA的算法相当简单。 在确保数据被归一化之后，输出仅仅是原始数据的协方差矩阵的奇异值分解。这段代码实现了主成分分析（PCA）算法，其任务是从输入数据集中提取主成分。具体来说，PCA通过对数据进行线性变换，将数据投影到一个新的坐标系中，使得新的坐标系的基向量（即主成分）按照数据的方差最大化的方向排列。这样可以用较少的主成分来描述数据的主要特征，从而实现降维和去噪。"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# 已知输入数据为X\n", "def pca(X):\n", "    \"\"\"\n", "    执行主成分分析（PCA）来提取数据集X的主成分。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        输入数据矩阵，假设每一行是一个数据样本，每一列是一个特征。\n", "        \n", "    返回:\n", "    U : numpy.ndarray\n", "        主成分方向（特征向量）。\n", "    S : numpy.n<PERSON><PERSON>\n", "        奇异值（对应于主成分方向的特征值）。\n", "    V : numpy.n<PERSON><PERSON>\n", "        V矩阵，用于SVD分解中的右奇异矩阵。\n", "    \"\"\"\n", "    \n", "    # 对输入数据减去均值并除以标准差\n", "    X = (X-X.mean())/X.std()\n", "    \n", "    # 将标准化后的数据使用np.matrix转换为矩阵形式（填空）\n", "    X = pass\n", "    \n", "    # 计算协方差矩阵表示各特征之间的线性关系。\n", "    #协方差矩阵的计算方式是先将数据矩阵转置，然后与自身相乘，再除以样本数。（填空）\n", "    cov = pass\n", "    \n", "    # 对协方差矩阵进行奇异值分解（SVD）\n", "    U, S, V = np.linalg.svd(cov)\n", "    \n", "    return U, S, V\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["(matrix([[-0.79241747, -0.60997914],\n", "         [-0.60997914,  0.79241747]]),\n", " array([1.43584536, 0.56415464]),\n", " matrix([[-0.79241747, -0.60997914],\n", "         [-0.60997914,  0.79241747]]))"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["U, S, V = pca(X)\n", "U, S, V"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们有主成分（矩阵U），我们可以用这些来将原始数据投影到一个较低维的空间中。 对于这个任务，我们将实现一个计算投影并且仅选择顶部K个分量的函数，有效地减少了维数。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def project_data(X, U, k):\n", "    \"\"\"\n", "    将原始数据X投影到由主成分矩阵U的前k个特征向量构成的子空间中。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        原始数据矩阵，每行代表一个样本，每列代表一个特征。\n", "    U : numpy.ndarray\n", "        主成分矩阵，每列是一个主成分（特征向量）。\n", "    k : int\n", "        要使用的主成分数量。\n", "    \n", "    返回:\n", "    projected_data : numpy.ndarray\n", "        投影后的数据，维度为 (m, k)，其中 m 是样本数量。\n", "    \"\"\"\n", "    \n", "    # 从主成分矩阵U中选择前k个主成分（填空）\n", "    U_reduced = pass\n", "    \n", "    # 将原始数据投影到选定的主成分空间中， 使用矩阵乘法来进行投影（填空）\n", "    projected_data = pass\n", "    \n", "    return projected_data\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-4.74689738],\n", "        [-7.15889408],\n", "        [-4.79563345],\n", "        [-4.45754509],\n", "        [-4.80263579],\n", "        [-7.04081342],\n", "        [-4.97025076],\n", "        [-8.75934561],\n", "        [-6.2232703 ],\n", "        [-7.04497331],\n", "        [-6.91702866],\n", "        [-6.79543508],\n", "        [-6.3438312 ],\n", "        [-6.99891495],\n", "        [-4.54558119],\n", "        [-8.31574426],\n", "        [-7.16920841],\n", "        [-5.08083842],\n", "        [-8.54077427],\n", "        [-6.94102769],\n", "        [-8.5978815 ],\n", "        [-5.76620067],\n", "        [-8.2020797 ],\n", "        [-6.23890078],\n", "        [-4.37943868],\n", "        [-5.56947441],\n", "        [-7.53865023],\n", "        [-7.70645413],\n", "        [-5.17158343],\n", "        [-6.19268884],\n", "        [-6.24385246],\n", "        [-8.02715303],\n", "        [-4.81235176],\n", "        [-7.07993347],\n", "        [-5.45953289],\n", "        [-7.60014707],\n", "        [-4.39612191],\n", "        [-7.82288033],\n", "        [-3.40498213],\n", "        [-6.54290343],\n", "        [-7.17879573],\n", "        [-5.22572421],\n", "        [-4.83081168],\n", "        [-7.23907851],\n", "        [-4.36164051],\n", "        [-6.44590096],\n", "        [-2.69118076],\n", "        [-4.61386195],\n", "        [-5.88236227],\n", "        [-7.76732508]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["Z = project_data(X, U, 1)\n", "Z"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以通过反向转换步骤来恢复原始数据。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def recover_data(Z, U, k):\n", "    \"\"\"\n", "    将投影后的数据Z恢复到原始特征空间中。\n", "    \n", "    参数:\n", "    Z : numpy.n<PERSON><PERSON>\n", "        投影后的数据矩阵，维度为 (m, k)，其中 m 是样本数量，k 是投影到的主成分数量。\n", "    U : numpy.ndarray\n", "        主成分矩阵，每列是一个主成分（特征向量）。\n", "    k : int\n", "        使用的主成分数量。\n", "    \n", "    返回:\n", "    recovered_data : numpy.ndarray\n", "        恢复到原始特征空间的数据矩阵，维度与原始数据X相同。\n", "    \"\"\"\n", "    \n", "    # 从主成分矩阵U中选择前k个主成分\n", "    U_reduced = U[:,:k]\n", "    \n", "    # 使用矩阵乘法将投影后的数据恢复到原始特征空间\n", "    # 这里使用U_reduced的转置，因为投影过程中是使用U_reduced而不是U\n", "    recovered_data = np.dot(Z, U_reduced.T)\n", "    \n", "    return recovered_data\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[3.76152442, 2.89550838],\n", "        [5.67283275, 4.36677606],\n", "        [3.80014373, 2.92523637],\n", "        [3.53223661, 2.71900952],\n", "        [3.80569251, 2.92950765],\n", "        [5.57926356, 4.29474931],\n", "        [3.93851354, 3.03174929],\n", "        [6.94105849, 5.3430181 ],\n", "        [4.93142811, 3.79606507],\n", "        [5.58255993, 4.29728676],\n", "        [5.48117436, 4.21924319],\n", "        [5.38482148, 4.14507365],\n", "        [5.02696267, 3.8696047 ],\n", "        [5.54606249, 4.26919213],\n", "        [3.60199795, 2.77270971],\n", "        [6.58954104, 5.07243054],\n", "        [5.681006  , 4.37306758],\n", "        [4.02614513, 3.09920545],\n", "        [6.76785875, 5.20969415],\n", "        [5.50019161, 4.2338821 ],\n", "        [6.81311151, 5.24452836],\n", "        [4.56923815, 3.51726213],\n", "        [6.49947125, 5.00309752],\n", "        [4.94381398, 3.80559934],\n", "        [3.47034372, 2.67136624],\n", "        [4.41334883, 3.39726321],\n", "        [5.97375815, 4.59841938],\n", "        [6.10672889, 4.70077626],\n", "        [4.09805306, 3.15455801],\n", "        [4.90719483, 3.77741101],\n", "        [4.94773778, 3.80861976],\n", "        [6.36085631, 4.8963959 ],\n", "        [3.81339161, 2.93543419],\n", "        [5.61026298, 4.31861173],\n", "        [4.32622924, 3.33020118],\n", "        [6.02248932, 4.63593118],\n", "        [3.48356381, 2.68154267],\n", "        [6.19898705, 4.77179382],\n", "        [2.69816733, 2.07696807],\n", "        [5.18471099, 3.99103461],\n", "        [5.68860316, 4.37891565],\n", "        [4.14095516, 3.18758276],\n", "        [3.82801958, 2.94669436],\n", "        [5.73637229, 4.41568689],\n", "        [3.45624014, 2.66050973],\n", "        [5.10784454, 3.93186513],\n", "        [2.13253865, 1.64156413],\n", "        [3.65610482, 2.81435955],\n", "        [4.66128664, 3.58811828],\n", "        [6.1549641 , 4.73790627]])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["X_recovered = recover_data(Z, U, 1)\n", "X_recovered"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(list(X_recovered[:, 0]), list(X_recovered[:, 1]))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请注意，第一主成分的投影轴基本上是数据集中的对角线。 当我们将数据减少到一个维度时，我们失去了该对角线周围的变化，所以在我们的再现中，一切都沿着该对角线。\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们在此练习中的最后一个任务是将PCA应用于脸部图像。 通过使用相同的降维技术，我们可以使用比原始图像少得多的数据来捕获图像的“本质”。这段代码加载了名为 \"ex7faces.mat\" 的MATLAB文件，其中包含了一个名为 'X' 的变量。这个变量可能是一个矩阵，代表着一组人脸图像数据。然后，它将加载的数据存储在变量 X 中，并打印出了 X 的形状。"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5000, 1024)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["faces = loadmat('data/ex7faces.mat')\n", "X = faces['X']\n", "X.shape"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def plot_n_image(X, n):\n", "    \"\"\"\n", "    绘制前n个图像。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        包含图像数据的矩阵，每行是一个图像，每列是一个像素。\n", "    n : int\n", "        要绘制的图像数量。n必须是一个平方数。\n", "    \"\"\"\n", "    \n", "    # 计算每个图像的大小（假设图像是正方形）\n", "    pic_size = int(np.sqrt(X.shape[1]))\n", "    \n", "    # 计算要绘制的图像在网格中的行数和列数\n", "    grid_size = int(np.sqrt(n))\n", "\n", "    # 从输入数据中选择前n个图像（填空）\n", "    first_n_images = pass\n", "\n", "    # 创建一个具有多个子图的网格\n", "    fig, ax_array = plt.subplots(nrows=grid_size,\n", "                                 ncols=grid_size,\n", "                                 sharey=True,\n", "                                 sharex=True,\n", "                                 figsize=(8, 8))\n", "\n", "    # 循环遍历网格中的每个子图，并在每个子图中绘制一个图像\n", "    for r in range(grid_size):\n", "        for c in range(grid_size):\n", "            # 显示图像\n", "            ax_array[r, c].imshow(first_n_images[grid_size * r + c].reshape((pic_size, pic_size)))\n", "            # 去除坐标轴刻度\n", "            plt.xticks(np.array([]))\n", "            plt.yticks(np.array([]))\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# 注意：X[3, :] 选择的是第四行数据，其中32*32 = 1024是该数据的长度\n", "face = np.reshape(X[3,:], (32, 32))"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAGdCAYAAABU0qcqAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8rg+JYAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAt+UlEQVR4nO3de3DV9Z3/8de5535CCLmVQLkoqAj7K1XMT8uqsAI742BldrTtzGLX0Z9udFbZblt2Wq3u7i+unWltOxT/WFe2M0Vbd4qOzhZXscTtFthCZdG2ZoViCZKEi+Sec8k5398f/sxuFPTzDjl8kvB8zJwZkrx55/O9nLzPN+fkdUJBEAQCAOA8C/teAADgwsQAAgB4wQACAHjBAAIAeMEAAgB4wQACAHjBAAIAeMEAAgB4EfW9gA/K5/M6duyYysvLFQqFfC8HAGAUBIH6+vrU0NCgcPjs1zkTbgAdO3ZMjY2NvpcBADhH7e3tmjlz5lm/XrABtGnTJn3zm99UZ2enlixZou9973u68sorP/b/lZeXS5KWX3afopGE0/fqWVDuvK6+mbbfOubcliBJyidsqUbZaTnn2nhlytS7tCTtXBsJG9c9HDHV972ddK6Nv2s7PqmZWffivPGKOm8rNzH+8jtU5H6uRGLutZKUG3ZfTDhiO1fyOfd9HuRsOyVkOG+DlO2cjfba6nO17ve3q+cfMvW+pvIt59pPJ46Yen8iWphnYfr687r0050jP8/PpiAD6Ec/+pE2bNigxx9/XMuWLdNjjz2mVatWqa2tTTU1NR/5f9//tVs0knAeQJFYkfPaIgnjDndvLRkHULjY/QdFuMTUWhFDvXUA5Y0DKFxUuOMTLjas5QIZQOG4bQAFWcMAihqjI4cnyAAKGc/ZjK0+KHbfznhZ3NS7uMz9x3RZkW0fVhRoAL3v455GKch3/9a3vqU77rhDX/ziF3XppZfq8ccfV0lJif7xH/+xEN8OADAJjfsAymQy2rdvn1auXPnf3yQc1sqVK7Vr164P1afTafX29o66AQCmvnEfQCdPnlQul1Ntbe2oz9fW1qqzs/ND9S0tLUomkyM3XoAAABcG738HtHHjRvX09Izc2tvbfS8JAHAejPuLEKqrqxWJRNTV1TXq811dXaqrq/tQfSKRUCJheKkZAGBKGPcroHg8rqVLl2rHjh0jn8vn89qxY4eamprG+9sBACapgrwMe8OGDVq/fr0+/elP68orr9Rjjz2mgYEBffGLXyzEtwMATEIFGUC33HKLTpw4oQceeECdnZ36gz/4A23fvv1DL0wAAFy4QkEQGP+yrLB6e3uVTCZ12f/5v4rE3f6AsXvRsHP/+DRbooBl75SX2nrPKB1wro1HbH9ceGKw1Lm2P2V7Dq6/q8xUn+hyf5yTrnU/lpIkwx8jRkptvcPGfW6Rzxn/0j5tqM8U8LVFxj9atvwhaiGFjfsklLWtO5Jxrw1sh165IsM+n+6eyCDZUhnWTt/vXDvYl9Ntn/pP9fT0qKKi4qx13l8FBwC4MDGAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXhQkC248ZD7Tq0iJW6zEFbUffqO7symNGjIzJJ1Ku0fa5ANbfEc45B6x0Z0qNvW2xOv0nyox9S7qsJ02obx7bbg0a+odjrjvw2lJ9+gjScrlCxcj09Prfl5JklLujxVDhYy/CRt7G459yLi/A0ss0AQKHIsM2bYz1uNeH+qw/Zz41RuLnGt3Jy9zrs2nUpL+82PruAICAHjBAAIAeMEAAgB4wQACAHjBAAIAeMEAAgB4wQACAHjBAAIAeMEAAgB4wQACAHjBAAIAeDFhs+D+V/1RxUrjTrURQ6aaVVHElk1m0Zspcq491W/Laxs84Z41Fj8RMfUO5UzlClt24Un3DDtJis50z3erK+sz9S6LumURStLR/kpT725DRpok00PFoMjY3JKpNmx7zBrKuOeYmbLdJNvDZ+M5a2WJgcxHrT+v3Jub7muS4r3utYlu99qcY+QmV0AAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8mbBRPfzahWNYtimd6YtC573Bgm7nJWMpUb/Hrrjrn2qF+W0SNJV4lsCXxmOM+LP2DmC2mZDjr3vzdIVuc0UUzjjvXpnK2u9JAZcxU3xN2j1bKZ42PKw1RVsa7j4K8+38I5Q15NpJkSBwKGyKBxqSAsUCWfW49PsPuaWCKGiKB8o7ZRFwBAQC8YAABALxgAAEAvGAAAQC8YAABALxgAAEAvGAAAQC8YAABALxgAAEAvGAAAQC8YAABALyYsFlwFbGU4nG3sKeGom7nvmURW7Zb3hCutOf0J029B08XO9dGim0BUrkB99ym0mO2nKzIkC2v7fTl7vWhZMbUO59zX/uJnjJT73CN+7rnlJ4y9a4r7jPV7w03Otee6EqaeocK+TDU8T4sSUHGtpDogHsOYMiYvzaRBIZcx3zMdl8OGfL0cpZaxyVzBQQA8GLcB9A3vvENhUKhUbeFCxeO97cBAExyBfkV3GWXXaaXX375v79JdML+pg8A4ElBJkM0GlVdnft73QAALjwFeQ7orbfeUkNDg+bOnasvfOELOnLkyFlr0+m0ent7R90AAFPfuA+gZcuWacuWLdq+fbs2b96sw4cP6zOf+Yz6+s78qp+WlhYlk8mRW2Oj+6t9AACT17gPoDVr1uhP/uRPtHjxYq1atUr/8i//ou7ubv34xz8+Y/3GjRvV09Mzcmtvbx/vJQEAJqCCvzqgsrJSF198sQ4ePHjGrycSCSUSiUIvAwAwwRT874D6+/t16NAh1dfXF/pbAQAmkXEfQF/60pfU2tqqt99+W7/4xS/02c9+VpFIRJ/73OfG+1sBACaxcf8V3NGjR/W5z31Op06d0owZM3TNNddo9+7dmjFjhqnPxaVdKiqNOdVOiw44950e6Teto3PYPdak7USNqbeG3ed/KDRsal18zP3QVr8+ZOp9+uIiU338E+7HJ2eI1pGkXNY9jsWqJOIeCzQv0WXqfSRTbarPDn/SuTaSsOXOBIaIFetj1sBwjoeNUTwWebcfJf/NEH8jSYbELlP8jSSF0+73ibDtx0TB1u1aO+4D6Omnnx7vlgCAKYgsOACAFwwgAIAXDCAAgBcMIACAFwwgAIAXDCAAgBcMIACAFwwgAIAXDCAAgBcMIACAFwV/O4axqot1qzjmtrx4yD37KmbMVHsnPc25dvBEqal3uDTrXDvcGzf1Tna5Z1nF3nzH1DvaONdUP2zIa8tlbNluoYh7QFVDVY+pd03M/d15a6JnfsPFs9ndN99UXxR3P1dilbYsuJ7+Yufa7JDtMWt40P14hrLGHMBi92MfxG3ZbjLmtZkeyht7h4bd92HY/TSRJEUH3WstOXOB4ynIFRAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwIsJG8VTE+lVadQWy+Iib5y5BwdmONfG3jWutzLlXBo9VWRqHRtwjx4JaqtMvYeqC/e4JUgZo3hK3XNNLqnsMvUuDw85176Zrjf1/m13rak+FnbfzlQmZuqdHXKvD5+wRUJZhBsHTPW5Qfe1RE/Y9knImNwTGFKEgqixuWUdxh9BuUSBah0nC1dAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8mbBbc3Fi3ymJu8/FU3j2kaNBQK0mn0yXOtZG0IRBKUqrfPcuqZMDWO1vqXtu+xpgFt8g9I02S8in30yycsj0mKqlNO9deWf47U+/KyKBz7c+6LzH1Tg3b7nrd/e7nYeYdw8GXVHzcfZ8P1eVMvedddsy5Nm3cJ0dP1jjXlnTY7j+lHe7Ze5LU1+i+D/vn2/ZhqMi9Pp+wZd5FBt33Sz7hnmGXT7ntP66AAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF5M2Cy4hmhCFVG3+RjLueeB/c6YBffuYLGp3mTYff6HM7bWsQH33KZUlS0nK8jb6i3ycVsG1yUzupxry8MpU+/unHv+2olUman3u722vDb9l3t92Snb8Rmsdz9Xque9a+pdHnPf58e63bPdrOK97tsoSdP2uGfYSdJA/UzDYmzneDjmXp8rsvUOZSPuvYvde+dFFhwAYAIzD6BXX31VN954oxoaGhQKhfTss8+O+noQBHrggQdUX1+v4uJirVy5Um+99dZ4rRcAMEWYB9DAwICWLFmiTZs2nfHrjz76qL773e/q8ccf1549e1RaWqpVq1YplbL9+gMAMLWZnwNas2aN1qxZc8avBUGgxx57TF/72te0du1aSdIPfvAD1dbW6tlnn9Wtt956bqsFAEwZ4/oc0OHDh9XZ2amVK1eOfC6ZTGrZsmXatWvXGf9POp1Wb2/vqBsAYOob1wHU2dkpSaqtrR31+dra2pGvfVBLS4uSyeTIrbGxcTyXBACYoLy/Cm7jxo3q6ekZubW3t/teEgDgPBjXAVRXVydJ6uoa/bcZXV1dI1/7oEQioYqKilE3AMDUN64DaM6cOaqrq9OOHTtGPtfb26s9e/aoqalpPL8VAGCSM78Krr+/XwcPHhz5+PDhw9q/f7+qqqo0a9Ys3Xffffrbv/1bXXTRRZozZ46+/vWvq6GhQTfddNN4rhsAMMmZB9DevXt13XXXjXy8YcMGSdL69eu1ZcsWffnLX9bAwIDuvPNOdXd365prrtH27dtVVFRk+j6JUEyJkNsFWlHIPaemN29bh0U+bov7CGXcI1OGjckt+ah771i/rbd6YrZ6w36JVtoyhxqLTzvXbj99ual3+0Clc23b2/Wm3rFO2z5M9Lgfz6E623kYnet+Asws7zb1zuTdf8RUlNj+VnBI7vFHJSdypt5K287DoRrDPs/YfvEU9Bh+TCdsUTxB1LDuAtSaB9C1116rIDh781AopIcfflgPP/ywtTUA4ALi/VVwAIALEwMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADghTmKZ7IbzCdM9VUlQ861p8ttOUzRQff5n7fkMEnKGTYzb4x2C6eNWVY5QxbcdFtm1/a3LzHVWwz2FDvXRk7b7krZmqypPpRzP0iBe2ycJKnckMEWDdvO8ZklJ51rw6Hppt79v6/9+KL/r+xXb5t65/sHTPXDFe77JTRsO0Cl7e73t0zSdt8cLjH8XMkb1u1YyxUQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMCLCRvF050bVC7nNh9zgS2mxuKTZe86175dbYsSyXUVOddGc7b4juFi9/qSLlu8SvJtW31/fcS5tjtWYuodrXaPSiotzph6D0XctzPxyT5T73ze9tgvudP9rhqEbefK8RlJ59q6Mtt2lkXSzrWd/eWm3hVvu8c2DR/rMPUOl5WZ6oMiQ4TUsO3Yl3a6n4fRIduxH2hwr82VGho7LpkrIACAFwwgAIAXDCAAgBcMIACAFwwgAIAXDCAAgBcMIACAFwwgAIAXDCAAgBcMIACAFwwgAIAXEzYLri/IS44RbzFD38rIoGkdlTH3+nl1J0y9+6sSzrVlcfdMLUlqP13pXDv0ZoWpd2TIdtqkatxzshrm2/ZhQ1mP+zpyljNFmjPtlHNtMp4y9f7F7+eY6vNR94yv6JAtGzF6wn2/tE+vNPWeWdLtXPtuty1/rX7YUFzAvEhJipa4LyZvzHWMDRrO28B2TZGudK/PzDA0dmzLFRAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwIsJG8UTky1ix1VRKGtbR8g9RqamuM/U+7oZ/+Vce01pm6n3jyqWOdd21tqieK6ofNtUXxt1j8vJGx8THc1UOddOiw6Yel+WeMe5tnM4aer95ukaU333bPeYmuQhW+xM0v001OlEpan33mijc20k6n5fswpFbT/qQiFbXE4u637ehsK24xNOu9cn0tZ96PcahCsgAIAXDCAAgBfmAfTqq6/qxhtvVENDg0KhkJ599tlRX7/tttsUCoVG3VavXj1e6wUATBHmATQwMKAlS5Zo06ZNZ61ZvXq1Ojo6Rm5PPfXUOS0SADD1mF+EsGbNGq1Zs+YjaxKJhOrq6sa8KADA1FeQ54B27typmpoaLViwQHfffbdOnTr7G3ul02n19vaOugEApr5xH0CrV6/WD37wA+3YsUN///d/r9bWVq1Zs0a53JlfHtjS0qJkMjlya2x0f9kmAGDyGve/A7r11ltH/n355Zdr8eLFmjdvnnbu3KkVK1Z8qH7jxo3asGHDyMe9vb0MIQC4ABT8Zdhz585VdXW1Dh48eMavJxIJVVRUjLoBAKa+gg+go0eP6tSpU6qvry/0twIATCLmX8H19/ePupo5fPiw9u/fr6qqKlVVVemhhx7SunXrVFdXp0OHDunLX/6y5s+fr1WrVo3rwgEAk5t5AO3du1fXXXfdyMfvP3+zfv16bd68WQcOHNA//dM/qbu7Ww0NDbrhhhv0N3/zN0okEuO36g+wpLulAlvCXDrvvovKohlT7/mJLufa8rCtd3e22Lm21LjuBYkOU/30SL9zbTawnZJ1hpy5KsM6JKnckBvYmy8y9e5P2e4PidPutSXHh029cwn3X4T09UVMvU/3lDrXhsJ5U+/hIkNeW8S2bhXbjqcCS3acLQsuknHfL0HElmFnurtFDcfHsdY8gK699loFwdl34IsvvmhtCQC4AJEFBwDwggEEAPCCAQQA8IIBBADwggEEAPCCAQQA8IIBBADwggEEAPCCAQQA8IIBBADwYtzfD2i8DAVSxDEyKRO4z9HuXIltHfm4e++Me/6aJP0uM8O5ti7abepdGRtyrv3liVmm3q2xhab6/11+5rfiOJPO4aSpt8WR0HRTvSXD7mC61tR7sN+WBTdtwD0/zBRLJilT5n7/CYyRamWlKefa7hNlpt6J3jO/yeWZBOm0qXcoavzRGHI/PiHjw35Lvls+Zjv4ubhh3dHxr+UKCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgxYSN4imUVBAz1Z9MlzrXtp2sMfV+/Xi9c+3W8BWm3t3vVDjXTt9ny1f5t5xtO39y1aeda2c0njb1jkfc41hihlpJmlHsHsVz6LQt5ifyTpGpvqI961xrjeLpn+n+ODRblzH1ri4bcK7tOe1+X5OkfKxwj5+t0T1Bzn0tuSHbunOG1CZrVFIo736yhCJ599qwWy1XQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvGEAAAC8YQAAALxhAAAAvJmwWXDQkxRxjigYC9znanSsxrcOS7zb0ZqWpd+Kkew5TuC8w9a7rda8fmmELD4ukbWspPex+mr07YMtUy5W651NFyt3z1CTpWDTpXBuPD5t6542ZXZG0e47dUHXc1Huoxv14xkttWXCRkPvxKS5PmXpni8uda0sShkC1MQj1ux/QcNr2uD8fdd+H2VLbfTmIuh97S95dkHer5QoIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAODFhI3iKQ+FVR5ym4/dhr6nh0tN6+g95h73Mf0tU2vlitxrI2lb73i/e3zHYK3tcUjPcltkSijkHveRz9gyasLdMefaXNTWOyf3+mzMdlcKpttigTquKnauHbalTSnc2O9eG7bFML075L6Yorhtn/TPdD9vq2pmmHpr2BatFMq5R+BEbGlGGqp2305rFI/ytuM53rgCAgB4YRpALS0tuuKKK1ReXq6amhrddNNNamtrG1WTSqXU3Nys6dOnq6ysTOvWrVNXV9e4LhoAMPmZBlBra6uam5u1e/duvfTSS8pms7rhhhs0MDAwUnP//ffr+eef1zPPPKPW1lYdO3ZMN99887gvHAAwuZl+cb19+/ZRH2/ZskU1NTXat2+fli9frp6eHj3xxBPaunWrrr/+eknSk08+qUsuuUS7d+/WVVddNX4rBwBMauf0HFBPT48kqaqqSpK0b98+ZbNZrVy5cqRm4cKFmjVrlnbt2nXGHul0Wr29vaNuAICpb8wDKJ/P67777tPVV1+tRYsWSZI6OzsVj8dVWVk5qra2tladnZ1n7NPS0qJkMjlya2xsHOuSAACTyJgHUHNzs9544w09/fTT57SAjRs3qqenZ+TW3t5+Tv0AAJPDmP4O6J577tELL7ygV199VTNnzhz5fF1dnTKZjLq7u0ddBXV1damuru6MvRKJhBIFfrtcAMDEY7oCCoJA99xzj7Zt26ZXXnlFc+bMGfX1pUuXKhaLaceOHSOfa2tr05EjR9TU1DQ+KwYATAmmK6Dm5mZt3bpVzz33nMrLy0ee10kmkyouLlYymdTtt9+uDRs2qKqqShUVFbr33nvV1NTEK+AAAKOYBtDmzZslSddee+2ozz/55JO67bbbJEnf/va3FQ6HtW7dOqXTaa1atUrf//73x2WxAICpwzSAguDjc4OKioq0adMmbdq0acyLKqTBXNxUHz/tngcWG7LlKg3Vuuc2xftMrZWPuvcuPumeGydJqWjOVB+Pu+dqRUtta9E099LShC2Eq2fIPaxvoMc9q02Swj22p18z09zPrcSCHlPvTyTd63vShgBDSf0p9+d3MxnbPhluMOQdXnrm56DPpqTtuKk+2u9+fwsP2/LaBm1LNzGtxZDp6FpLFhwAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwIsxvR3D+dAX5CXH5Ie+vHs8SD6wxWCEM+71qWm23tFB99q88UhFUu4xJcNF7nFDkpQbttUPGeoDYxJP/Qz3GJmLKk+YeueS7sfz7ZLppt5HQ4YMIUlBp3ukTX9Hmal3T1HauXZ6seGklZTKxJxrB4bcayVJEfdomP5P2HoXdZSY6qOD7udKutp4kufde8cMkUCSFM661wYD7j+EgiG3Wq6AAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF5M2Cy4d4ZLVDrsNh87hyud+4ZD7vlRki0rKW2L95IMsU3DxbaMp5oX33Gujc+aYerdmbblasVLM8612RPFpt4n4u65Z9YsuNpEn3Ntf7F7VpskZWtsj/0Gk3Hn2r4jFabene1VzrWx2bYcs8bKbudaa05j72C5c+3QDGPvBUlTveW+nCu17cOQIY8yn7KdV5GUIWeu2z3TMZ9yq+UKCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgxYSN4nk3V6pUzi3OIRYadu6bzts2ebjYPbonsCXUaLjEPZIj2m97rJCrcc8F6p9li78JRdyjdSQpHnc/PpminKl3psc9AicaskWgdGdL3HuHbb2TiZSpvr6017m2I2E7PifedY/u6TztHn8jSflKWwSOSZH7Ps8kbRFcJxfb7m/5uHt/S6SNJIXT7vswkja1NtUXnXRfR85xzVwBAQC8YAABALxgAAEAvGAAAQC8YAABALxgAAEAvGAAAQC8YAABALxgAAEAvGAAAQC8YAABALyYsFlwbel6FcXcwtVqYz3OffuGi0zryMxwzyYLp2zzPJR3z1bK1mVNvY9dl3SuTVXbcrKKy4yBUxbGh0Qhwz5P520ZXJWxIefarpQtI83KkjVnzZnLVbrvw6GMLfDwWKd7JqF6jT+OStzvm8Nltqw+83k4bMhrS9ny8aKD7rWxAVNrxfpt931XuYxbX66AAABemAZQS0uLrrjiCpWXl6umpkY33XST2traRtVce+21CoVCo2533XXXuC4aADD5mQZQa2urmpubtXv3br300kvKZrO64YYbNDAw+rrvjjvuUEdHx8jt0UcfHddFAwAmP9MvXbdv3z7q4y1btqimpkb79u3T8uXLRz5fUlKiurq68VkhAGBKOqfngHp63nvyv6qqatTnf/jDH6q6ulqLFi3Sxo0bNTh49mfR0um0ent7R90AAFPfmF8Fl8/ndd999+nqq6/WokWLRj7/+c9/XrNnz1ZDQ4MOHDigr3zlK2pra9NPfvKTM/ZpaWnRQw89NNZlAAAmqTEPoObmZr3xxhv6+c9/Purzd95558i/L7/8ctXX12vFihU6dOiQ5s2b96E+Gzdu1IYNG0Y+7u3tVWNj41iXBQCYJMY0gO655x698MILevXVVzVz5syPrF22bJkk6eDBg2ccQIlEQolEYizLAABMYqYBFASB7r33Xm3btk07d+7UnDlzPvb/7N+/X5JUX18/pgUCAKYm0wBqbm7W1q1b9dxzz6m8vFydnZ2SpGQyqeLiYh06dEhbt27VH//xH2v69Ok6cOCA7r//fi1fvlyLFy8uyAYAACYn0wDavHmzpPf+2PR/evLJJ3XbbbcpHo/r5Zdf1mOPPaaBgQE1NjZq3bp1+trXvjZuCwYATA3mX8F9lMbGRrW2tp7Tgt73u8EZiofjTrUnY2XOfXOBLYcpVDzsXjzktt73BRH3HKZIwj33SpKyhmiySNq2T/J526v30yn30yzcY3taMmSM+LKw5AZmcracuXjEdjwHh93PrcGs7TxMZ933eTptOz6BJSMtbTuvjKdhQQVhS6aa7f4WGLYznC1Mtptku6+51k6gQwgAuJAwgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF6M+f2ACu2dwaSiIbe3aTgVLSnYOkorUs61/UOF2535U7Z4lVzCPZIj3mOLBhk4VWyqD2Xc+8d7jTE/jRnn2rKoe60kHR2sdK7tz9jeUsQa3TOQcj/+mXTM1DuXdd/ngaFWkkIp9+0MorYYmfCQJaOmcBE1VpEh2/0tbEgDs8T2SFLechoaTquc4yZyBQQA8IIBBADwggEEAPCCAQQA8IIBBADwggEEAPCCAQQA8IIBBADwggEEAPCCAQQA8IIBBADwYsJmwVkMB7ZcLYvieNa5dqAoZ+odDLqvO2LJvZIUNeRNhWzLVvFR22ljOTzDJbbMrrr60861/cO2PL2hYffwq+PdZabe2X7bWjRsOP55W2uLcKZwj1lDxnWH3e+aiqRt684bc+lCeff7W3TQ1FoyLCWI2HLmLPl7IUMmnes5yBUQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMCLCRvFE4vkFYu4ZcTkA/f4ieG8beZGwu75ILFiQzaIpIwh1sQSmSFJ0QFDNMiQrXc+Zov7sMjVZkz1yUTKufb4ULmpd3/GPS4nO2iL1gkZYpgkKWyIkglixuMZL1x2TxB2X4tlGyVbhJQ1biqatZ3jlv6RtG0tloiiUM4aIWSoNbR2reUKCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAOAFAwgA4AUDCADgBQMIAODFhM2CSw1HNTwcc6pND7tvhiU3TpJihiy4qooBU+93DbWZqC1rLOhwr88lbPskXW3LDssn3EOkisvds90kqSdd5FwbsYRZSeruL3EvNuT6SVIoZ8was+xyY+5ZKO++FmsmoQqUNSbJ9PA5sEXvmbPjbHlttt4W4WFbvSWXLuf241iS5PpjlisgAIAXpgG0efNmLV68WBUVFaqoqFBTU5N++tOfjnw9lUqpublZ06dPV1lZmdatW6eurq5xXzQAYPIzDaCZM2fqkUce0b59+7R3715df/31Wrt2rX79619Lku6//349//zzeuaZZ9Ta2qpjx47p5ptvLsjCAQCTm+k5oBtvvHHUx3/3d3+nzZs3a/fu3Zo5c6aeeOIJbd26Vddff70k6cknn9Qll1yi3bt366qrrhq/VQMAJr0xPweUy+X09NNPa2BgQE1NTdq3b5+y2axWrlw5UrNw4ULNmjVLu3btOmufdDqt3t7eUTcAwNRnHkCvv/66ysrKlEgkdNddd2nbtm269NJL1dnZqXg8rsrKylH1tbW16uzsPGu/lpYWJZPJkVtjY6N5IwAAk495AC1YsED79+/Xnj17dPfdd2v9+vX6zW9+M+YFbNy4UT09PSO39vb2MfcCAEwe5r8Disfjmj9/viRp6dKl+uUvf6nvfOc7uuWWW5TJZNTd3T3qKqirq0t1dXVn7ZdIJJRIJOwrBwBMauf8d0D5fF7pdFpLly5VLBbTjh07Rr7W1tamI0eOqKmp6Vy/DQBgijFdAW3cuFFr1qzRrFmz1NfXp61bt2rnzp168cUXlUwmdfvtt2vDhg2qqqpSRUWF7r33XjU1NfEKOADAh5gG0PHjx/Wnf/qn6ujoUDKZ1OLFi/Xiiy/qj/7ojyRJ3/72txUOh7Vu3Tql02mtWrVK3//+98e0sN8dqlO42C1mJT7NPb4lHrdlVUQNUTxhQ+179e7ZI/HSjKl3Zpp7bkY4Y4uFsUTrSFJg2M5M2pD3Iak7734RnzdEzkhSZsA9zig8ZIziGTbu87hhn9tam9di6l3A2BlLqlZg/V1P4XaJQnnb/ccS82Pdzpzh2Q9LZFcu4lZrGkBPPPHER369qKhImzZt0qZNmyxtAQAXILLgAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXjCAAABeMIAAAF4wgAAAXpjTsAstCN6Lqcin3ON18gn32tywLRskZIjXCYxRPLmUeyRHYIyRUSri3tsaxTNk24eWKJ583BY5lIu5r8W6D/NDhuOZst2VrPE3QbRwUTyFZIriyRoXbomosfa2nYYKsu614UzhonisLL1zIUMUT+a9n8nv/zw/6/cPPq7iPDt69ChvSgcAU0B7e7tmzpx51q9PuAGUz+d17NgxlZeXK/Q/Jm5vb68aGxvV3t6uiooKjyssLLZz6rgQtlFiO6ea8djOIAjU19enhoYGhcNnf6Znwv0KLhwOf+TErKiomNIH/31s59RxIWyjxHZONee6nclk8mNreBECAMALBhAAwItJM4ASiYQefPBBJRKGd1CahNjOqeNC2EaJ7Zxqzud2TrgXIQAALgyT5goIADC1MIAAAF4wgAAAXjCAAABeTJoBtGnTJn3yk59UUVGRli1bpv/4j//wvaRx9Y1vfEOhUGjUbeHChb6XdU5effVV3XjjjWpoaFAoFNKzzz476utBEOiBBx5QfX29iouLtXLlSr311lt+FnsOPm47b7vttg8d29WrV/tZ7Bi1tLToiiuuUHl5uWpqanTTTTepra1tVE0qlVJzc7OmT5+usrIyrVu3Tl1dXZ5WPDYu23nttdd+6HjeddddnlY8Nps3b9bixYtH/ti0qalJP/3pT0e+fr6O5aQYQD/60Y+0YcMGPfjgg/rVr36lJUuWaNWqVTp+/LjvpY2ryy67TB0dHSO3n//8576XdE4GBga0ZMkSbdq06Yxff/TRR/Xd735Xjz/+uPbs2aPS0lKtWrVKKUMQ7UTwcdspSatXrx51bJ966qnzuMJz19raqubmZu3evVsvvfSSstmsbrjhBg0MDIzU3H///Xr++ef1zDPPqLW1VceOHdPNN9/scdV2LtspSXfccceo4/noo496WvHYzJw5U4888oj27dunvXv36vrrr9fatWv161//WtJ5PJbBJHDllVcGzc3NIx/ncrmgoaEhaGlp8biq8fXggw8GS5Ys8b2MgpEUbNu2beTjfD4f1NXVBd/85jdHPtfd3R0kEongqaee8rDC8fHB7QyCIFi/fn2wdu1aL+splOPHjweSgtbW1iAI3jt2sVgseOaZZ0Zqfvvb3waSgl27dvla5jn74HYGQRD84R/+YfAXf/EX/hZVINOmTQv+4R/+4bweywl/BZTJZLRv3z6tXLly5HPhcFgrV67Url27PK5s/L311ltqaGjQ3Llz9YUvfEFHjhzxvaSCOXz4sDo7O0cd12QyqWXLlk254ypJO3fuVE1NjRYsWKC7775bp06d8r2kc9LT0yNJqqqqkiTt27dP2Wx21PFcuHChZs2aNamP5we3830//OEPVV1drUWLFmnjxo0aHBz0sbxxkcvl9PTTT2tgYEBNTU3n9VhOuDDSDzp58qRyuZxqa2tHfb62tlZvvvmmp1WNv2XLlmnLli1asGCBOjo69NBDD+kzn/mM3njjDZWXl/te3rjr7OyUpDMe1/e/NlWsXr1aN998s+bMmaNDhw7pr//6r7VmzRrt2rVLkYj7+zZNFPl8Xvfdd5+uvvpqLVq0SNJ7xzMej6uysnJU7WQ+nmfaTkn6/Oc/r9mzZ6uhoUEHDhzQV77yFbW1teknP/mJx9Xavf7662pqalIqlVJZWZm2bdumSy+9VPv37z9vx3LCD6ALxZo1a0b+vXjxYi1btkyzZ8/Wj3/8Y91+++0eV4Zzdeutt478+/LLL9fixYs1b9487dy5UytWrPC4srFpbm7WG2+8Memfo/w4Z9vOO++8c+Tfl19+uerr67VixQodOnRI8+bNO9/LHLMFCxZo//796unp0T//8z9r/fr1am1tPa9rmPC/gquurlYkEvnQKzC6urpUV1fnaVWFV1lZqYsvvlgHDx70vZSCeP/YXWjHVZLmzp2r6urqSXls77nnHr3wwgv62c9+NuptU+rq6pTJZNTd3T2qfrIez7Nt55ksW7ZMkibd8YzH45o/f76WLl2qlpYWLVmyRN/5znfO67Gc8AMoHo9r6dKl2rFjx8jn8vm8duzYoaamJo8rK6z+/n4dOnRI9fX1vpdSEHPmzFFdXd2o49rb26s9e/ZM6eMqvfeuv6dOnZpUxzYIAt1zzz3atm2bXnnlFc2ZM2fU15cuXapYLDbqeLa1tenIkSOT6nh+3Haeyf79+yVpUh3PM8nn80qn0+f3WI7rSxoK5Omnnw4SiUSwZcuW4De/+U1w5513BpWVlUFnZ6fvpY2bv/zLvwx27twZHD58OPj3f//3YOXKlUF1dXVw/Phx30sbs76+vuC1114LXnvttUBS8K1vfSt47bXXgt///vdBEATBI488ElRWVgbPPfdccODAgWDt2rXBnDlzgqGhIc8rt/mo7ezr6wu+9KUvBbt27QoOHz4cvPzyy8GnPvWp4KKLLgpSqZTvpTu7++67g2QyGezcuTPo6OgYuQ0ODo7U3HXXXcGsWbOCV155Jdi7d2/Q1NQUNDU1eVy13cdt58GDB4OHH3442Lt3b3D48OHgueeeC+bOnRssX77c88ptvvrVrwatra3B4cOHgwMHDgRf/epXg1AoFPzrv/5rEATn71hOigEUBEHwve99L5g1a1YQj8eDK6+8Mti9e7fvJY2rW265Jaivrw/i8XjwiU98IrjllluCgwcP+l7WOfnZz34WSPrQbf369UEQvPdS7K9//etBbW1tkEgkghUrVgRtbW1+Fz0GH7Wdg4ODwQ033BDMmDEjiMViwezZs4M77rhj0j14OtP2SQqefPLJkZqhoaHgz//8z4Np06YFJSUlwWc/+9mgo6PD36LH4OO288iRI8Hy5cuDqqqqIJFIBPPnzw/+6q/+Kujp6fG7cKM/+7M/C2bPnh3E4/FgxowZwYoVK0aGTxCcv2PJ2zEAALyY8M8BAQCmJgYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwIv/B1txOTy9T264AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(face) ## 使用imshow函数显示人脸图像\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看起来很糟糕。 这些只有32 x 32灰度的图像（它也是侧面渲染，但我们现在可以忽略）。 我们的下一步是在面数据集上运行PCA，并取得前100个主要特征。"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["U, S, V = pca(X)\n", "Z = project_data(X, U, 100)  #将原始数据X投影到由主成分矩阵U的前k个特征向量构成的子空间中。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们可以尝试恢复原来的结构并再次渲染。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X_recovered = recover_data(Z, U, 100)\n", "face = np.reshape(X_recovered[3,:], (32, 32))\n", "plt.imshow(face)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以看到：数据维度减少，但细节并没有怎么损失。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["练习：定义一个 pca() 函数，该函数实现了主成分分析（PCA）算法。然后，生成了一个虚拟的数据集 X，其中包含100个样本和3个特征。接下来，调用 pca() 函数对数据进行降维，保留了2个主成分。最后，输出原始数据矩阵和降维后的数据矩阵的形状。"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["\n", "def pca(X, k):\n", "    \"\"\"\n", "    使用主成分分析（PCA）对数据集进行降维和特征提取。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        输入数据矩阵，每行代表一个样本，每列代表一个特征。\n", "    k : int\n", "        要保留的主成分数量。\n", "    \n", "    返回:\n", "    X_reduced : numpy.n<PERSON><PERSON>\n", "        降维后的数据矩阵，每行代表一个样本，每列代表一个特征（主成分）。\n", "    \"\"\"\n", "    # 标准化数据\n", "    # 列方向计算均值axis=0（填空）\n", "    X_mean = pass\n", "    #标准差\n", "    X_std = np.std(X, axis=0)\n", "    # 将原始数据矩阵X的每个元素减去对应特征的均值，再除以对应特征的标准差，实现数据标准化（填空）\n", "    X_normalized =pass\n", "    \n", "    # 计算协方差矩阵\n", "    cov_matrix = np.cov(X_normalized, rowvar=False)\n", "    \n", "    # 对协方差矩阵进行奇异值分解（填空）\n", "    U, S, V = pass\n", "    \n", "    # 选择前k个主成分\n", "    U_reduced = U[:, :k]\n", "    \n", "    # 使用主成分矩阵将数据投影到新的低维空间中（填空）\n", "    X_reduced = np.dot(X_normalized,U_reduced)\n", "    \n", "    return X_reduced\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据矩阵形状: (100, 3)\n", "降维后的数据矩阵形状: (100, 2)\n"]}], "source": ["# 生成一个虚拟数据集\n", "np.random.seed(0)\n", "X = np.random.rand(100, 3)  # 100个样本，每个样本3个特征\n", "\n", "# 调用PCA函数对数据进行降维\n", "k = 2  # 保留2个主成分\n", "X_reduced = pca(X, k)\n", "\n", "print(\"原始数据矩阵形状:\", X.shape)\n", "print(\"降维后的数据矩阵形状:\", X_reduced.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 1}