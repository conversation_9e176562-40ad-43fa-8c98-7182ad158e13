{"cells": [{"cell_type": "markdown", "id": "99e31966", "metadata": {}, "source": ["# k-means的python实现"]}, {"cell_type": "code", "execution_count": 4, "id": "7a857131", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final Centroids:\n", " [[1.5        2.        ]\n", " [4.33333333 5.66666667]]\n", "Cluster 1 (Centroid 1): ['A', 'B', 'C']\n", "Cluster 2 (Centroid 2): ['D', 'E', 'F']\n"]}], "source": ["import numpy as np\n", "\n", "# 定义包含六个点的二维数据集\n", "points = np.array([\n", "    [1.0, 1.0],  # A\n", "    [1.5, 2.0],  # B\n", "    [2.0, 3.0],  # C\n", "    [5.0, 7.0],  # D\n", "    [3.5, 5.0],  # E\n", "    [4.5, 5.0]  # F\n", "])\n", "\n", "# 初始质心选择\n", "centroids = np.array([\n", "    [1.0, 1.0],  # 质心1\n", "    [5.0, 7.0]  # 质心2\n", "])\n", "\n", "def compute_distances(points, centroids):\n", "    #按行axis=1计算每个点与每个质心的欧氏距离。（填空）\n", "    distances = np.zeros((points.shape[0], centroids.shape[0]))\n", "    for i, point in enumerate(points):\n", "        distances[i] = np.linalg.norm(point - centroids[i])\n", "    return distances\n", "\n", "def update_clusters(distances):\n", "    #使用np.argmin，根据距离矩阵为每个点分配最近的质心\n", "    #行方向返回数组中最小值的索引。（填空）\n", "    return np.argmin(distances, axis=1)\n", "\n", "def update_centroids(points, clusters, k):\n", "    #计算新的质心，作为每个簇中所有点的均值。\n", "    new_centroids = np.zeros((k, points.shape[1]))\n", "    # 确保簇不为空（填空）\n", "    for i in range(k):\n", "        if :\n", "            new_centroids[i] = points[clusters == i].mean(axis=0)\n", "    return new_centroids\n", "\n", "def k_means(points, initial_centroids, max_iterations=100, tol=1e-4):\n", "    #运行K-均值算法，执行多次迭代直到质心不再发生明显变化。\n", "    ## 复制初始质心，避免修改原始数据（填空）\n", "    centroids = pass\n", "    for iteration in range(max_iterations):\n", "        # 步骤1: 计算每个点到当前质心的距离（填空）\n", "        distances = pass\n", "        # 步骤2: 根据距离分配每个点到最近的簇（填空）\n", "        clusters = pass\n", "         # 步骤3: 根据新的簇分配计算新的质心，centroids.shape[0]，确保新质心数组的行数与原质心数量一致。\n", "        new_centroids = update_centroids(points, clusters, centroids.shape[0]) \n", "\n", "        # 检查收敛条件\n", "        if np.allclose(new_centroids, centroids, atol=tol):\n", "            break\n", "        centroids = new_centroids\n", "    return centroids, clusters\n", "\n", "\n", "# 运行K-均值算法（填空）\n", "final_centroids, final_clusters = pass\n", "\n", "# 输出结果,列出每个簇包含的点\n", "clusters_A_to_F = ['A', 'B', 'C', 'D', 'E', 'F']\n", "cluster_1_points = [clusters_A_to_F[i] for i in range(len(final_clusters)) if final_clusters[i] == 0]\n", "cluster_2_points = [clusters_A_to_F[i] for i in range(len(final_clusters)) if final_clusters[i] == 1]\n", "\n", "print(\"Final Centroids:\\n\", final_centroids)\n", "print(\"Cluster 1 (Centroid 1):\", cluster_1_points)\n", "print(\"Cluster 2 (Centroid 2):\", cluster_2_points)\n"]}, {"cell_type": "markdown", "id": "87f9508a", "metadata": {}, "source": ["# k-means的sklearn实现"]}, {"cell_type": "code", "execution_count": 2, "id": "b513c5e1", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Cluster 1 Points: ['D', 'E', 'F']\n", "Cluster 2 Points: ['A', 'B', 'C']\n", "Computed Centroids:\n", " [[4.33333333 5.66666667]\n", " [1.5        2.        ]]\n"]}], "source": ["from sklearn.cluster import KMeans\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 定义包含六个点的二维数据集\n", "points = np.array([\n", "    [1.0, 1.0],  # A\n", "    [1.5, 2.0],  # B\n", "    [2.0, 3.0],  # C\n", "    [5.0, 7.0],  # D\n", "    [3.5, 5.0],  # E\n", "    [4.5, 5.0]   # F\n", "])\n", "\n", "# 初始化KMeans类，设定n_clusters参数将点分成两个簇\n", "kmeans = KMeans(n_clusters=2, init='k-means++', random_state=0)\n", "\n", "# 执行聚类算法（填空）\n", "pass\n", "\n", "# 获取每个点所属的聚类标签\n", "labels = kmeans.labels_\n", "\n", "# 获取计算得到的质心\n", "centroids = kmeans.cluster_centers_\n", "\n", "# 可视化\n", "plt.figure(figsize=(8, 6))\n", "# 为每一个簇的点使用不同颜色\n", "colors = ['r', 'b']\n", "\n", "# 绘制数据点并根据其标签上色\n", "for idx, (point, label) in enumerate(zip(points, labels)):\n", "    plt.scatter(point[0], point[1], c=colors[label], label=f\"Point {chr(65+idx)}\" if idx < 6 else \"\")\n", "\n", "# 绘制质心\n", "for centroid, color in zip(centroids, colors):\n", "    plt.scatter(centroid[0], centroid[1], c=color, marker='x', s=200, linewidths=3, label='Centroid')\n", "\n", "# 添加坐标轴标签和标题\n", "plt.xlabel('X-coordinate')\n", "plt.ylabel('Y-coordinate')\n", "plt.title('K-Means Clustering of 2D Points')\n", "plt.legend(loc='best')\n", "plt.grid(True)\n", "# 标示每个点\n", "for idx, (point) in enumerate(points):\n", "    plt.text(point[0], point[1], chr(65+idx), fontsize=12, ha='right')\n", "\n", "plt.show()\n", "\n", "# 打印输出用于确认结果\n", "clusters_A_to_F = ['A', 'B', 'C', 'D', 'E', 'F']\n", "cluster_1_points = [clusters_A_to_F[i] for i in range(len(labels)) if labels[i] == 0]\n", "cluster_2_points = [clusters_A_to_F[i] for i in range(len(labels)) if labels[i] == 1]\n", "\n", "print(\"Cluster 1 Points:\", cluster_1_points)\n", "print(\"Cluster 2 Points:\", cluster_2_points)\n", "print(\"Computed Centroids:\\n\", centroids)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a68794f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}