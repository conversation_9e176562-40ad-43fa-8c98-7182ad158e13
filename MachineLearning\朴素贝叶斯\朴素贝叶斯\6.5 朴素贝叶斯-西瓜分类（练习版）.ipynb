import pandas as pd

# 读取数据文件
def readfile():
    datasets = pd.read_csv(r'3.0.csv', encoding="UTF-8")
    # 删除无关列编号列（填空）
    pass
    return datasets


# 朴素贝叶斯分类器
def NaiveBayes(test):
    # 读取数据
    data = readfile()
    # 转换为列表形式
    data = data.values.tolist()
    #创建好瓜与坏瓜列表（填空）
    pass
    # 将数据分为好瓜和坏瓜两类（填空）
    for i in range(len(data)):
        if pass:
            goodMelon.append(data[i])
        else:
            badMelon.append(data[i])
    # 计算类别1和类别2的似然度p1，p2
    p1 = 1.0; p2 = 1.0
    for j in range(len(test)):
        x = 0.0  
        for k in range(len(goodMelon)):
             # 检查当前好瓜样本的第 j 个特征是否与测试数据的第 j 个特征相同（填空）
            if pass
                x = x + 1.0
            # 使用拉普拉斯平滑计算在好瓜类别下当前特征取值的概率，并累乘到 p1 中
        p1 = p1 * ((x + 1.0) / (len(goodMelon) + 2.0))  # 拉普拉斯平滑，其中 2 是一个常数，常用于拉普拉斯平滑的参数选择。
    for j in range(len(test)):
        x = 0.0
        for k in range(len(badMelon)):
            # 检查当前坏瓜样本的第 j 个特征是否与测试数据的第 j 个特征相同
            if pass
                x = x + 1.0
            #获取p2（填空）
        p2 = pass # 拉普拉斯平滑
    # 计算类别1和类别2的先验概率，好（坏）瓜样本数 / 总样本数
    pc1 = pass
    pc2 = pass
    # 使用贝叶斯公式计算后验概率（填空）
    p_good = pass
    p_bad = pass
    # 输出预测结果
    if p_good > p_bad:
        print('好瓜')
    else:
        print('坏瓜')



if __name__ == '__main__':
    # 测试数据
    test = ['青绿', '蜷缩', '浊响', '清晰', '凹陷', '硬滑', 0.697, 0.460]
    # 进行预测
    NaiveBayes(test)







