{"cells": [{"cell_type": "markdown", "id": "2e8f4f85", "metadata": {}, "source": ["# 机器学习练习-集成学习\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1403a6e9", "metadata": {"scrolled": true}, "outputs": [], "source": ["#pip install lightgbm -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 2, "id": "3257f67d", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "from sklearn.model_selection import train_test_split \n", "from sklearn.datasets import make_hastie_10_2    #数据生成函数包含10个特征的2分类数据集"]}, {"cell_type": "code", "execution_count": 3, "id": "02071243", "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "id": "cc304344", "metadata": {}, "source": ["## 生成数据\n", "生成12000行的数据，训练集和测试集按照3:1划分"]}, {"cell_type": "code", "execution_count": 4, "id": "6ebd8121", "metadata": {}, "outputs": [], "source": ["data, target = make_hastie_10_2()"]}, {"cell_type": "code", "execution_count": 5, "id": "41cca920", "metadata": {}, "outputs": [], "source": ["# 每个标签的值为 -1 或 1，将-1替换为0，以适应常见的二分类标签格式\n", "target[target == -1] = 0\n", "# 确保target数据类型为整数（填空）\n", "target = target.astype(int)"]}, {"cell_type": "code", "execution_count": 6, "id": "c6dfeb30", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["((9000, 10), (3000, 10))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train, X_test, y_train, y_test = train_test_split(data, target, random_state=42)\n", "X_train.shape, X_test.shape"]}, {"cell_type": "markdown", "id": "579ac5c7", "metadata": {}, "source": ["## 模型对比\n", "对比六大模型，都使用默认参数"]}, {"cell_type": "code", "execution_count": 7, "id": "f808ac16", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.48233333 (+/- 0.00), 耗时0.03秒。模型名称[Logistic Regression]\n", "Accuracy: 0.88111111 (+/- 0.01), 耗时10.83秒。模型名称[Random Forest]\n", "Accuracy: 0.87844444 (+/- 0.01), 耗时2.18秒。模型名称[AdaBoost]\n", "Accuracy: 0.91066667 (+/- 0.01), 耗时8.88秒。模型名称[GBDT]\n", "Accuracy: 0.92522222 (+/- 0.00), 耗时3.57秒。模型名称[XGBoost]\n", "[LightGBM] [Info] Number of positive: 3665, number of negative: 3535\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000279 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2550\n", "[LightGBM] [Info] Number of data points in the train set: 7200, number of used features: 10\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.509028 -> initscore=0.036115\n", "[LightGBM] [Info] Start training from score 0.036115\n", "[LightGBM] [Info] Number of positive: 3665, number of negative: 3535\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000264 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2550\n", "[LightGBM] [Info] Number of data points in the train set: 7200, number of used features: 10\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.509028 -> initscore=0.036115\n", "[LightGBM] [Info] Start training from score 0.036115\n", "[LightGBM] [Info] Number of positive: 3665, number of negative: 3535\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000259 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2550\n", "[LightGBM] [Info] Number of data points in the train set: 7200, number of used features: 10\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.509028 -> initscore=0.036115\n", "[LightGBM] [Info] Start training from score 0.036115\n", "[LightGBM] [Info] Number of positive: 3665, number of negative: 3535\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000256 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2550\n", "[LightGBM] [Info] Number of data points in the train set: 7200, number of used features: 10\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.509028 -> initscore=0.036115\n", "[LightGBM] [Info] Start training from score 0.036115\n", "[LightGBM] [Info] Number of positive: 3664, number of negative: 3536\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000242 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2550\n", "[LightGBM] [Info] Number of data points in the train set: 7200, number of used features: 10\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.508889 -> initscore=0.035559\n", "[LightGBM] [Info] Start training from score 0.035559\n", "Accuracy: 0.93044444 (+/- 0.00), 耗时0.55秒。模型名称[LightGBM]\n"]}], "source": ["# 导入必要的库和模块\n", "from sklearn.linear_model import LogisticRegression  # 逻辑回归\n", "from sklearn.ensemble import RandomForestClassifier  # 随机森林分类器\n", "from sklearn.ensemble import AdaBoostClassifier  # AdaBoost分类器\n", "from sklearn.ensemble import GradientBoostingClassifier  # 梯度提升分类器\n", "from xgboost import XGBClassifier  # XGBoost分类器\n", "from lightgbm import LGBMClassifier  # LightGBM分类器\n", "from sklearn.model_selection import cross_val_score  # 交叉验证评分\n", "import time  # 计时\n", "\n", "# 初始化分类器(填空)\n", "clf1 = LogisticRegression()  # 初始化逻辑回归分类器\n", "clf2 = RandomForestClassifier()  # 初始化随机森林分类器\n", "clf3 = AdaBoostClassifier()  # 初始化AdaBoost分类器\n", "clf4 = GradientBoostingClassifier()  # 初始化梯度提升分类器\n", "clf5 = XGBClassifier()  # 初始化XGBoost分类器\n", "clf6 = LGBMClassifier()  # 初始化LightGBM分类器\n", "\n", "# 为每个分类器进行交叉验证并打印结果\n", "for clf, label in zip([clf1, clf2, clf3, clf4, clf5, clf6], [\n", "        'Logistic Regression', 'Random Forest', 'AdaBoost', 'GBDT', 'XGBoost', 'LightGBM'\n", "]):\n", "    start = time.time()  # 记录开始时间\n", "    scores = cross_val_score(clf, X_train, y_train, scoring='accuracy', cv=5)  # 进行5折交叉验证，计算分类器的准确率\n", "    end = time.time()  # 记录结束时间\n", "    # 计算运行时间（填空）\n", "    running_time = end-start\n", "    print(\"Accuracy: %0.8f (+/- %0.2f), 耗时%0.2f秒。模型名称[%s]\"  #占位符\n", "           %(scores.mean(), scores.std(), running_time, label))  \n"]}, {"cell_type": "markdown", "id": "caa60220", "metadata": {}, "source": ["对比了六大模型，可以看出，逻辑回归速度最快，但准确率最低。\n", "而LightGBM，速度快，而且准确率最高，所以，现在处理结构化数据的时候，大部分都是用LightGBM算法。"]}, {"cell_type": "markdown", "id": "d63e7313", "metadata": {}, "source": ["## XGBoost的使用"]}, {"cell_type": "markdown", "id": "9ef5b733", "metadata": {}, "source": ["### 1.原生XGBoost的使用"]}, {"cell_type": "code", "execution_count": 8, "id": "96d20084", "metadata": {}, "outputs": [], "source": ["import xgboost as xgb\n", "#记录程序运行时间\n", "import time\n", "\n", "start_time = time.time()    #开始运行时间\n", "\n", "#xgb矩阵赋值（填空）\n", "xgb_train = xgb.DMatrix(X_train, y_train)\n", "xgb_test = xgb.DMatrix(X_test,y_test)\n", "##参数\n", "params = {\n", "    'booster': 'gbtree', \n", "    'eta': 0.007,  # 如同学习率\n", "    'min_child_weight': 3,  #最小子叶节点权重。\n", "    'max_depth': 6,  # 构建树的深度，越大越容易过拟合\n", "    'gamma': 0.1,  # 树的叶子节点上作进一步分区所需的最小损失减少,越大越保守，一般0.1、0.2这样子。\n", "    'subsample': 0.7,  # 随机采样训练样本比例\n", "    'colsample_bytree': 0.7,  # 生成树时进行的列采样 比例\n", "    'lambda': 2,  # 控制模型复杂度的权重值的L2正则化项参数，参数越大，模型越不容易过拟合。\n", "    'seed': 1000,  #随机种子\n", "}\n", "plst = list(params.items())   #将参数转换为一个列表，以便XGBoost可以使用。\n", "num_rounds = 500  # 迭代次数\n", "watchlist = [(xgb_train, 'train'), (xgb_test, 'val')]  #监视列表，用于追踪训练和验证数据集的性能。"]}, {"cell_type": "code", "execution_count": 9, "id": "7421bd6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\ttrain-rmse:0.49937\tval-rmse:0.49945\n", "[1]\ttrain-rmse:0.49869\tval-rmse:0.49888\n", "[2]\ttrain-rmse:0.49801\tval-rmse:0.49832\n", "[3]\ttrain-rmse:0.49736\tval-rmse:0.49777\n", "[4]\ttrain-rmse:0.49671\tval-rmse:0.49726\n", "[5]\ttrain-rmse:0.49607\tval-rmse:0.49673\n", "[6]\ttrain-rmse:0.49544\tval-rmse:0.49615\n", "[7]\ttrain-rmse:0.49479\tval-rmse:0.49564\n", "[8]\ttrain-rmse:0.49417\tval-rmse:0.49509\n", "[9]\ttrain-rmse:0.49352\tval-rmse:0.49458\n", "[10]\ttrain-rmse:0.49288\tval-rmse:0.49401\n", "[11]\ttrain-rmse:0.49224\tval-rmse:0.49348\n", "[12]\ttrain-rmse:0.49160\tval-rmse:0.49297\n", "[13]\ttrain-rmse:0.49094\tval-rmse:0.49243\n", "[14]\ttrain-rmse:0.49031\tval-rmse:0.49191\n", "[15]\ttrain-rmse:0.48969\tval-rmse:0.49140\n", "[16]\ttrain-rmse:0.48907\tval-rmse:0.49089\n", "[17]\ttrain-rmse:0.48847\tval-rmse:0.49039\n", "[18]\ttrain-rmse:0.48788\tval-rmse:0.48985\n", "[19]\ttrain-rmse:0.48726\tval-rmse:0.48934\n", "[20]\ttrain-rmse:0.48664\tval-rmse:0.48882\n", "[21]\ttrain-rmse:0.48606\tval-rmse:0.48828\n", "[22]\ttrain-rmse:0.48546\tval-rmse:0.48779\n", "[23]\ttrain-rmse:0.48486\tval-rmse:0.48728\n", "[24]\ttrain-rmse:0.48429\tval-rmse:0.48679\n", "[25]\ttrain-rmse:0.48372\tval-rmse:0.48632\n", "[26]\ttrain-rmse:0.48314\tval-rmse:0.48585\n", "[27]\ttrain-rmse:0.48254\tval-rmse:0.48535\n", "[28]\ttrain-rmse:0.48195\tval-rmse:0.48487\n", "[29]\ttrain-rmse:0.48137\tval-rmse:0.48434\n", "[30]\ttrain-rmse:0.48078\tval-rmse:0.48383\n", "[31]\ttrain-rmse:0.48020\tval-rmse:0.48339\n", "[32]\ttrain-rmse:0.47961\tval-rmse:0.48290\n", "[33]\ttrain-rmse:0.47903\tval-rmse:0.48241\n", "[34]\ttrain-rmse:0.47845\tval-rmse:0.48192\n", "[35]\ttrain-rmse:0.47788\tval-rmse:0.48142\n", "[36]\ttrain-rmse:0.47734\tval-rmse:0.48096\n", "[37]\ttrain-rmse:0.47676\tval-rmse:0.48048\n", "[38]\ttrain-rmse:0.47617\tval-rmse:0.48003\n", "[39]\ttrain-rmse:0.47560\tval-rmse:0.47953\n", "[40]\ttrain-rmse:0.47506\tval-rmse:0.47904\n", "[41]\ttrain-rmse:0.47450\tval-rmse:0.47857\n", "[42]\ttrain-rmse:0.47394\tval-rmse:0.47806\n", "[43]\ttrain-rmse:0.47338\tval-rmse:0.47757\n", "[44]\ttrain-rmse:0.47283\tval-rmse:0.47708\n", "[45]\ttrain-rmse:0.47222\tval-rmse:0.47659\n", "[46]\ttrain-rmse:0.47166\tval-rmse:0.47610\n", "[47]\ttrain-rmse:0.47113\tval-rmse:0.47566\n", "[48]\ttrain-rmse:0.47059\tval-rmse:0.47519\n", "[49]\ttrain-rmse:0.47004\tval-rmse:0.47475\n", "[50]\ttrain-rmse:0.46948\tval-rmse:0.47428\n", "[51]\ttrain-rmse:0.46892\tval-rmse:0.47382\n", "[52]\ttrain-rmse:0.46836\tval-rmse:0.47334\n", "[53]\ttrain-rmse:0.46782\tval-rmse:0.47285\n", "[54]\ttrain-rmse:0.46730\tval-rmse:0.47237\n", "[55]\ttrain-rmse:0.46675\tval-rmse:0.47186\n", "[56]\ttrain-rmse:0.46622\tval-rmse:0.47140\n", "[57]\ttrain-rmse:0.46568\tval-rmse:0.47094\n", "[58]\ttrain-rmse:0.46513\tval-rmse:0.47046\n", "[59]\ttrain-rmse:0.46458\tval-rmse:0.47000\n", "[60]\ttrain-rmse:0.46405\tval-rmse:0.46959\n", "[61]\ttrain-rmse:0.46351\tval-rmse:0.46910\n", "[62]\ttrain-rmse:0.46297\tval-rmse:0.46861\n", "[63]\ttrain-rmse:0.46242\tval-rmse:0.46812\n", "[64]\ttrain-rmse:0.46189\tval-rmse:0.46769\n", "[65]\ttrain-rmse:0.46133\tval-rmse:0.46719\n", "[66]\ttrain-rmse:0.46081\tval-rmse:0.46676\n", "[67]\ttrain-rmse:0.46026\tval-rmse:0.46630\n", "[68]\ttrain-rmse:0.45970\tval-rmse:0.46583\n", "[69]\ttrain-rmse:0.45919\tval-rmse:0.46541\n", "[70]\ttrain-rmse:0.45864\tval-rmse:0.46497\n", "[71]\ttrain-rmse:0.45808\tval-rmse:0.46445\n", "[72]\ttrain-rmse:0.45752\tval-rmse:0.46403\n", "[73]\ttrain-rmse:0.45699\tval-rmse:0.46355\n", "[74]\ttrain-rmse:0.45645\tval-rmse:0.46302\n", "[75]\ttrain-rmse:0.45592\tval-rmse:0.46263\n", "[76]\ttrain-rmse:0.45540\tval-rmse:0.46219\n", "[77]\ttrain-rmse:0.45489\tval-rmse:0.46173\n", "[78]\ttrain-rmse:0.45439\tval-rmse:0.46127\n", "[79]\ttrain-rmse:0.45389\tval-rmse:0.46080\n", "[80]\ttrain-rmse:0.45337\tval-rmse:0.46040\n", "[81]\ttrain-rmse:0.45283\tval-rmse:0.46000\n", "[82]\ttrain-rmse:0.45233\tval-rmse:0.45957\n", "[83]\ttrain-rmse:0.45179\tval-rmse:0.45908\n", "[84]\ttrain-rmse:0.45126\tval-rmse:0.45862\n", "[85]\ttrain-rmse:0.45076\tval-rmse:0.45822\n", "[86]\ttrain-rmse:0.45023\tval-rmse:0.45776\n", "[87]\ttrain-rmse:0.44976\tval-rmse:0.45737\n", "[88]\ttrain-rmse:0.44926\tval-rmse:0.45692\n", "[89]\ttrain-rmse:0.44873\tval-rmse:0.45648\n", "[90]\ttrain-rmse:0.44820\tval-rmse:0.45604\n", "[91]\ttrain-rmse:0.44770\tval-rmse:0.45565\n", "[92]\ttrain-rmse:0.44721\tval-rmse:0.45522\n", "[93]\ttrain-rmse:0.44671\tval-rmse:0.45482\n", "[94]\ttrain-rmse:0.44622\tval-rmse:0.45435\n", "[95]\ttrain-rmse:0.44571\tval-rmse:0.45397\n", "[96]\ttrain-rmse:0.44520\tval-rmse:0.45349\n", "[97]\ttrain-rmse:0.44473\tval-rmse:0.45309\n", "[98]\ttrain-rmse:0.44421\tval-rmse:0.45266\n", "[99]\ttrain-rmse:0.44372\tval-rmse:0.45224\n", "[100]\ttrain-rmse:0.44325\tval-rmse:0.45181\n", "[101]\ttrain-rmse:0.44276\tval-rmse:0.45138\n", "[102]\ttrain-rmse:0.44226\tval-rmse:0.45093\n", "[103]\ttrain-rmse:0.44178\tval-rmse:0.45052\n", "[104]\ttrain-rmse:0.44132\tval-rmse:0.45011\n", "[105]\ttrain-rmse:0.44082\tval-rmse:0.44968\n", "[106]\ttrain-rmse:0.44032\tval-rmse:0.44921\n", "[107]\ttrain-rmse:0.43983\tval-rmse:0.44876\n", "[108]\ttrain-rmse:0.43937\tval-rmse:0.44833\n", "[109]\ttrain-rmse:0.43887\tval-rmse:0.44792\n", "[110]\ttrain-rmse:0.43841\tval-rmse:0.44755\n", "[111]\ttrain-rmse:0.43794\tval-rmse:0.44716\n", "[112]\ttrain-rmse:0.43745\tval-rmse:0.44669\n", "[113]\ttrain-rmse:0.43698\tval-rmse:0.44630\n", "[114]\ttrain-rmse:0.43648\tval-rmse:0.44587\n", "[115]\ttrain-rmse:0.43602\tval-rmse:0.44548\n", "[116]\ttrain-rmse:0.43554\tval-rmse:0.44505\n", "[117]\ttrain-rmse:0.43506\tval-rmse:0.44464\n", "[118]\ttrain-rmse:0.43457\tval-rmse:0.44423\n", "[119]\ttrain-rmse:0.43409\tval-rmse:0.44378\n", "[120]\ttrain-rmse:0.43364\tval-rmse:0.44342\n", "[121]\ttrain-rmse:0.43316\tval-rmse:0.44301\n", "[122]\ttrain-rmse:0.43267\tval-rmse:0.44260\n", "[123]\ttrain-rmse:0.43220\tval-rmse:0.44223\n", "[124]\ttrain-rmse:0.43174\tval-rmse:0.44183\n", "[125]\ttrain-rmse:0.43129\tval-rmse:0.44143\n", "[126]\ttrain-rmse:0.43081\tval-rmse:0.44104\n", "[127]\ttrain-rmse:0.43033\tval-rmse:0.44066\n", "[128]\ttrain-rmse:0.42986\tval-rmse:0.44026\n", "[129]\ttrain-rmse:0.42940\tval-rmse:0.43986\n", "[130]\ttrain-rmse:0.42892\tval-rmse:0.43944\n", "[131]\ttrain-rmse:0.42847\tval-rmse:0.43904\n", "[132]\ttrain-rmse:0.42803\tval-rmse:0.43867\n", "[133]\ttrain-rmse:0.42756\tval-rmse:0.43826\n", "[134]\ttrain-rmse:0.42711\tval-rmse:0.43788\n", "[135]\ttrain-rmse:0.42669\tval-rmse:0.43752\n", "[136]\ttrain-rmse:0.42625\tval-rmse:0.43713\n", "[137]\ttrain-rmse:0.42580\tval-rmse:0.43670\n", "[138]\ttrain-rmse:0.42537\tval-rmse:0.43631\n", "[139]\ttrain-rmse:0.42489\tval-rmse:0.43590\n", "[140]\ttrain-rmse:0.42447\tval-rmse:0.43551\n", "[141]\ttrain-rmse:0.42398\tval-rmse:0.43510\n", "[142]\ttrain-rmse:0.42353\tval-rmse:0.43469\n", "[143]\ttrain-rmse:0.42310\tval-rmse:0.43431\n", "[144]\ttrain-rmse:0.42266\tval-rmse:0.43394\n", "[145]\ttrain-rmse:0.42221\tval-rmse:0.43361\n", "[146]\ttrain-rmse:0.42176\tval-rmse:0.43324\n", "[147]\ttrain-rmse:0.42132\tval-rmse:0.43289\n", "[148]\ttrain-rmse:0.42088\tval-rmse:0.43255\n", "[149]\ttrain-rmse:0.42047\tval-rmse:0.43221\n", "[150]\ttrain-rmse:0.42004\tval-rmse:0.43186\n", "[151]\ttrain-rmse:0.41963\tval-rmse:0.43152\n", "[152]\ttrain-rmse:0.41918\tval-rmse:0.43119\n", "[153]\ttrain-rmse:0.41876\tval-rmse:0.43084\n", "[154]\ttrain-rmse:0.41833\tval-rmse:0.43042\n", "[155]\ttrain-rmse:0.41789\tval-rmse:0.43006\n", "[156]\ttrain-rmse:0.41745\tval-rmse:0.42969\n", "[157]\ttrain-rmse:0.41702\tval-rmse:0.42930\n", "[158]\ttrain-rmse:0.41658\tval-rmse:0.42889\n", "[159]\ttrain-rmse:0.41616\tval-rmse:0.42850\n", "[160]\ttrain-rmse:0.41571\tval-rmse:0.42812\n", "[161]\ttrain-rmse:0.41528\tval-rmse:0.42774\n", "[162]\ttrain-rmse:0.41486\tval-rmse:0.42740\n", "[163]\ttrain-rmse:0.41446\tval-rmse:0.42709\n", "[164]\ttrain-rmse:0.41404\tval-rmse:0.42677\n", "[165]\ttrain-rmse:0.41365\tval-rmse:0.42645\n", "[166]\ttrain-rmse:0.41322\tval-rmse:0.42616\n", "[167]\ttrain-rmse:0.41281\tval-rmse:0.42586\n", "[168]\ttrain-rmse:0.41237\tval-rmse:0.42550\n", "[169]\ttrain-rmse:0.41195\tval-rmse:0.42514\n", "[170]\ttrain-rmse:0.41155\tval-rmse:0.42480\n", "[171]\ttrain-rmse:0.41113\tval-rmse:0.42445\n", "[172]\ttrain-rmse:0.41075\tval-rmse:0.42410\n", "[173]\ttrain-rmse:0.41029\tval-rmse:0.42374\n", "[174]\ttrain-rmse:0.40988\tval-rmse:0.42334\n", "[175]\ttrain-rmse:0.40946\tval-rmse:0.42299\n", "[176]\ttrain-rmse:0.40903\tval-rmse:0.42265\n", "[177]\ttrain-rmse:0.40862\tval-rmse:0.42231\n", "[178]\ttrain-rmse:0.40824\tval-rmse:0.42199\n", "[179]\ttrain-rmse:0.40783\tval-rmse:0.42165\n", "[180]\ttrain-rmse:0.40740\tval-rmse:0.42131\n", "[181]\ttrain-rmse:0.40701\tval-rmse:0.42095\n", "[182]\ttrain-rmse:0.40661\tval-rmse:0.42061\n", "[183]\ttrain-rmse:0.40619\tval-rmse:0.42030\n", "[184]\ttrain-rmse:0.40576\tval-rmse:0.41991\n", "[185]\ttrain-rmse:0.40534\tval-rmse:0.41953\n", "[186]\ttrain-rmse:0.40495\tval-rmse:0.41918\n", "[187]\ttrain-rmse:0.40454\tval-rmse:0.41881\n", "[188]\ttrain-rmse:0.40413\tval-rmse:0.41850\n", "[189]\ttrain-rmse:0.40373\tval-rmse:0.41818\n", "[190]\ttrain-rmse:0.40333\tval-rmse:0.41787\n", "[191]\ttrain-rmse:0.40294\tval-rmse:0.41755\n", "[192]\ttrain-rmse:0.40258\tval-rmse:0.41722\n", "[193]\ttrain-rmse:0.40220\tval-rmse:0.41692\n", "[194]\ttrain-rmse:0.40179\tval-rmse:0.41659\n", "[195]\ttrain-rmse:0.40141\tval-rmse:0.41625\n", "[196]\ttrain-rmse:0.40098\tval-rmse:0.41589\n", "[197]\ttrain-rmse:0.40060\tval-rmse:0.41552\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[198]\ttrain-rmse:0.40021\tval-rmse:0.41515\n", "[199]\ttrain-rmse:0.39985\tval-rmse:0.41486\n", "[200]\ttrain-rmse:0.39946\tval-rmse:0.41453\n", "[201]\ttrain-rmse:0.39906\tval-rmse:0.41417\n", "[202]\ttrain-rmse:0.39865\tval-rmse:0.41385\n", "[203]\ttrain-rmse:0.39827\tval-rmse:0.41352\n", "[204]\ttrain-rmse:0.39787\tval-rmse:0.41323\n", "[205]\ttrain-rmse:0.39753\tval-rmse:0.41296\n", "[206]\ttrain-rmse:0.39712\tval-rmse:0.41262\n", "[207]\ttrain-rmse:0.39674\tval-rmse:0.41231\n", "[208]\ttrain-rmse:0.39634\tval-rmse:0.41195\n", "[209]\ttrain-rmse:0.39595\tval-rmse:0.41160\n", "[210]\ttrain-rmse:0.39559\tval-rmse:0.41129\n", "[211]\ttrain-rmse:0.39519\tval-rmse:0.41101\n", "[212]\ttrain-rmse:0.39480\tval-rmse:0.41067\n", "[213]\ttrain-rmse:0.39443\tval-rmse:0.41037\n", "[214]\ttrain-rmse:0.39404\tval-rmse:0.41004\n", "[215]\ttrain-rmse:0.39367\tval-rmse:0.40975\n", "[216]\ttrain-rmse:0.39327\tval-rmse:0.40941\n", "[217]\ttrain-rmse:0.39291\tval-rmse:0.40911\n", "[218]\ttrain-rmse:0.39255\tval-rmse:0.40879\n", "[219]\ttrain-rmse:0.39219\tval-rmse:0.40849\n", "[220]\ttrain-rmse:0.39182\tval-rmse:0.40818\n", "[221]\ttrain-rmse:0.39144\tval-rmse:0.40787\n", "[222]\ttrain-rmse:0.39107\tval-rmse:0.40754\n", "[223]\ttrain-rmse:0.39068\tval-rmse:0.40725\n", "[224]\ttrain-rmse:0.39032\tval-rmse:0.40692\n", "[225]\ttrain-rmse:0.38995\tval-rmse:0.40661\n", "[226]\ttrain-rmse:0.38957\tval-rmse:0.40636\n", "[227]\ttrain-rmse:0.38919\tval-rmse:0.40608\n", "[228]\ttrain-rmse:0.38881\tval-rmse:0.40575\n", "[229]\ttrain-rmse:0.38847\tval-rmse:0.40549\n", "[230]\ttrain-rmse:0.38813\tval-rmse:0.40520\n", "[231]\ttrain-rmse:0.38777\tval-rmse:0.40492\n", "[232]\ttrain-rmse:0.38738\tval-rmse:0.40456\n", "[233]\ttrain-rmse:0.38702\tval-rmse:0.40422\n", "[234]\ttrain-rmse:0.38665\tval-rmse:0.40389\n", "[235]\ttrain-rmse:0.38630\tval-rmse:0.40359\n", "[236]\ttrain-rmse:0.38591\tval-rmse:0.40329\n", "[237]\ttrain-rmse:0.38554\tval-rmse:0.40300\n", "[238]\ttrain-rmse:0.38520\tval-rmse:0.40271\n", "[239]\ttrain-rmse:0.38485\tval-rmse:0.40240\n", "[240]\ttrain-rmse:0.38447\tval-rmse:0.40209\n", "[241]\ttrain-rmse:0.38410\tval-rmse:0.40178\n", "[242]\ttrain-rmse:0.38372\tval-rmse:0.40146\n", "[243]\ttrain-rmse:0.38340\tval-rmse:0.40118\n", "[244]\ttrain-rmse:0.38305\tval-rmse:0.40092\n", "[245]\ttrain-rmse:0.38269\tval-rmse:0.40062\n", "[246]\ttrain-rmse:0.38234\tval-rmse:0.40035\n", "[247]\ttrain-rmse:0.38200\tval-rmse:0.40004\n", "[248]\ttrain-rmse:0.38166\tval-rmse:0.39975\n", "[249]\ttrain-rmse:0.38131\tval-rmse:0.39945\n", "[250]\ttrain-rmse:0.38096\tval-rmse:0.39915\n", "[251]\ttrain-rmse:0.38060\tval-rmse:0.39888\n", "[252]\ttrain-rmse:0.38026\tval-rmse:0.39861\n", "[253]\ttrain-rmse:0.37990\tval-rmse:0.39831\n", "[254]\ttrain-rmse:0.37955\tval-rmse:0.39801\n", "[255]\ttrain-rmse:0.37919\tval-rmse:0.39771\n", "[256]\ttrain-rmse:0.37885\tval-rmse:0.39742\n", "[257]\ttrain-rmse:0.37852\tval-rmse:0.39716\n", "[258]\ttrain-rmse:0.37821\tval-rmse:0.39689\n", "[259]\ttrain-rmse:0.37785\tval-rmse:0.39660\n", "[260]\ttrain-rmse:0.37750\tval-rmse:0.39631\n", "[261]\ttrain-rmse:0.37716\tval-rmse:0.39603\n", "[262]\ttrain-rmse:0.37683\tval-rmse:0.39574\n", "[263]\ttrain-rmse:0.37652\tval-rmse:0.39545\n", "[264]\ttrain-rmse:0.37618\tval-rmse:0.39517\n", "[265]\ttrain-rmse:0.37584\tval-rmse:0.39491\n", "[266]\ttrain-rmse:0.37551\tval-rmse:0.39465\n", "[267]\ttrain-rmse:0.37517\tval-rmse:0.39436\n", "[268]\ttrain-rmse:0.37483\tval-rmse:0.39407\n", "[269]\ttrain-rmse:0.37448\tval-rmse:0.39378\n", "[270]\ttrain-rmse:0.37412\tval-rmse:0.39345\n", "[271]\ttrain-rmse:0.37380\tval-rmse:0.39318\n", "[272]\ttrain-rmse:0.37348\tval-rmse:0.39294\n", "[273]\ttrain-rmse:0.37317\tval-rmse:0.39266\n", "[274]\ttrain-rmse:0.37285\tval-rmse:0.39240\n", "[275]\ttrain-rmse:0.37250\tval-rmse:0.39211\n", "[276]\ttrain-rmse:0.37216\tval-rmse:0.39183\n", "[277]\ttrain-rmse:0.37181\tval-rmse:0.39155\n", "[278]\ttrain-rmse:0.37147\tval-rmse:0.39131\n", "[279]\ttrain-rmse:0.37115\tval-rmse:0.39105\n", "[280]\ttrain-rmse:0.37082\tval-rmse:0.39077\n", "[281]\ttrain-rmse:0.37050\tval-rmse:0.39049\n", "[282]\ttrain-rmse:0.37015\tval-rmse:0.39024\n", "[283]\ttrain-rmse:0.36979\tval-rmse:0.38993\n", "[284]\ttrain-rmse:0.36945\tval-rmse:0.38961\n", "[285]\ttrain-rmse:0.36913\tval-rmse:0.38934\n", "[286]\ttrain-rmse:0.36881\tval-rmse:0.38909\n", "[287]\ttrain-rmse:0.36850\tval-rmse:0.38885\n", "[288]\ttrain-rmse:0.36816\tval-rmse:0.38855\n", "[289]\ttrain-rmse:0.36785\tval-rmse:0.38831\n", "[290]\ttrain-rmse:0.36752\tval-rmse:0.38804\n", "[291]\ttrain-rmse:0.36718\tval-rmse:0.38775\n", "[292]\ttrain-rmse:0.36688\tval-rmse:0.38748\n", "[293]\ttrain-rmse:0.36655\tval-rmse:0.38720\n", "[294]\ttrain-rmse:0.36621\tval-rmse:0.38694\n", "[295]\ttrain-rmse:0.36590\tval-rmse:0.38669\n", "[296]\ttrain-rmse:0.36558\tval-rmse:0.38639\n", "[297]\ttrain-rmse:0.36526\tval-rmse:0.38614\n", "[298]\ttrain-rmse:0.36496\tval-rmse:0.38590\n", "[299]\ttrain-rmse:0.36462\tval-rmse:0.38561\n", "[300]\ttrain-rmse:0.36433\tval-rmse:0.38540\n", "[301]\ttrain-rmse:0.36400\tval-rmse:0.38514\n", "[302]\ttrain-rmse:0.36369\tval-rmse:0.38488\n", "[303]\ttrain-rmse:0.36337\tval-rmse:0.38463\n", "[304]\ttrain-rmse:0.36304\tval-rmse:0.38436\n", "[305]\ttrain-rmse:0.36275\tval-rmse:0.38412\n", "[306]\ttrain-rmse:0.36242\tval-rmse:0.38383\n", "[307]\ttrain-rmse:0.36211\tval-rmse:0.38360\n", "[308]\ttrain-rmse:0.36179\tval-rmse:0.38336\n", "[309]\ttrain-rmse:0.36147\tval-rmse:0.38311\n", "[310]\ttrain-rmse:0.36117\tval-rmse:0.38284\n", "[311]\ttrain-rmse:0.36088\tval-rmse:0.38260\n", "[312]\ttrain-rmse:0.36060\tval-rmse:0.38233\n", "[313]\ttrain-rmse:0.36027\tval-rmse:0.38208\n", "[314]\ttrain-rmse:0.35998\tval-rmse:0.38182\n", "[315]\ttrain-rmse:0.35968\tval-rmse:0.38159\n", "[316]\ttrain-rmse:0.35935\tval-rmse:0.38130\n", "[317]\ttrain-rmse:0.35903\tval-rmse:0.38106\n", "[318]\ttrain-rmse:0.35872\tval-rmse:0.38084\n", "[319]\ttrain-rmse:0.35839\tval-rmse:0.38061\n", "[320]\ttrain-rmse:0.35809\tval-rmse:0.38039\n", "[321]\ttrain-rmse:0.35779\tval-rmse:0.38014\n", "[322]\ttrain-rmse:0.35749\tval-rmse:0.37990\n", "[323]\ttrain-rmse:0.35720\tval-rmse:0.37964\n", "[324]\ttrain-rmse:0.35689\tval-rmse:0.37940\n", "[325]\ttrain-rmse:0.35660\tval-rmse:0.37917\n", "[326]\ttrain-rmse:0.35631\tval-rmse:0.37894\n", "[327]\ttrain-rmse:0.35602\tval-rmse:0.37873\n", "[328]\ttrain-rmse:0.35571\tval-rmse:0.37846\n", "[329]\ttrain-rmse:0.35541\tval-rmse:0.37823\n", "[330]\ttrain-rmse:0.35514\tval-rmse:0.37805\n", "[331]\ttrain-rmse:0.35482\tval-rmse:0.37782\n", "[332]\ttrain-rmse:0.35451\tval-rmse:0.37758\n", "[333]\ttrain-rmse:0.35419\tval-rmse:0.37732\n", "[334]\ttrain-rmse:0.35393\tval-rmse:0.37707\n", "[335]\ttrain-rmse:0.35364\tval-rmse:0.37686\n", "[336]\ttrain-rmse:0.35335\tval-rmse:0.37665\n", "[337]\ttrain-rmse:0.35305\tval-rmse:0.37643\n", "[338]\ttrain-rmse:0.35275\tval-rmse:0.37623\n", "[339]\ttrain-rmse:0.35245\tval-rmse:0.37600\n", "[340]\ttrain-rmse:0.35213\tval-rmse:0.37577\n", "[341]\ttrain-rmse:0.35185\tval-rmse:0.37555\n", "[342]\ttrain-rmse:0.35156\tval-rmse:0.37534\n", "[343]\ttrain-rmse:0.35128\tval-rmse:0.37509\n", "[344]\ttrain-rmse:0.35101\tval-rmse:0.37485\n", "[345]\ttrain-rmse:0.35072\tval-rmse:0.37461\n", "[346]\ttrain-rmse:0.35046\tval-rmse:0.37442\n", "[347]\ttrain-rmse:0.35015\tval-rmse:0.37415\n", "[348]\ttrain-rmse:0.34988\tval-rmse:0.37392\n", "[349]\ttrain-rmse:0.34957\tval-rmse:0.37366\n", "[350]\ttrain-rmse:0.34930\tval-rmse:0.37342\n", "[351]\ttrain-rmse:0.34902\tval-rmse:0.37321\n", "[352]\ttrain-rmse:0.34873\tval-rmse:0.37297\n", "[353]\ttrain-rmse:0.34844\tval-rmse:0.37276\n", "[354]\ttrain-rmse:0.34817\tval-rmse:0.37254\n", "[355]\ttrain-rmse:0.34788\tval-rmse:0.37232\n", "[356]\ttrain-rmse:0.34760\tval-rmse:0.37210\n", "[357]\ttrain-rmse:0.34731\tval-rmse:0.37191\n", "[358]\ttrain-rmse:0.34703\tval-rmse:0.37170\n", "[359]\ttrain-rmse:0.34676\tval-rmse:0.37147\n", "[360]\ttrain-rmse:0.34646\tval-rmse:0.37123\n", "[361]\ttrain-rmse:0.34618\tval-rmse:0.37103\n", "[362]\ttrain-rmse:0.34591\tval-rmse:0.37081\n", "[363]\ttrain-rmse:0.34565\tval-rmse:0.37063\n", "[364]\ttrain-rmse:0.34538\tval-rmse:0.37042\n", "[365]\ttrain-rmse:0.34511\tval-rmse:0.37020\n", "[366]\ttrain-rmse:0.34485\tval-rmse:0.36998\n", "[367]\ttrain-rmse:0.34457\tval-rmse:0.36978\n", "[368]\ttrain-rmse:0.34430\tval-rmse:0.36955\n", "[369]\ttrain-rmse:0.34401\tval-rmse:0.36931\n", "[370]\ttrain-rmse:0.34375\tval-rmse:0.36910\n", "[371]\ttrain-rmse:0.34348\tval-rmse:0.36887\n", "[372]\ttrain-rmse:0.34324\tval-rmse:0.36865\n", "[373]\ttrain-rmse:0.34296\tval-rmse:0.36846\n", "[374]\ttrain-rmse:0.34268\tval-rmse:0.36823\n", "[375]\ttrain-rmse:0.34240\tval-rmse:0.36800\n", "[376]\ttrain-rmse:0.34214\tval-rmse:0.36781\n", "[377]\ttrain-rmse:0.34186\tval-rmse:0.36760\n", "[378]\ttrain-rmse:0.34160\tval-rmse:0.36740\n", "[379]\ttrain-rmse:0.34133\tval-rmse:0.36720\n", "[380]\ttrain-rmse:0.34106\tval-rmse:0.36700\n", "[381]\ttrain-rmse:0.34082\tval-rmse:0.36682\n", "[382]\ttrain-rmse:0.34056\tval-rmse:0.36660\n", "[383]\ttrain-rmse:0.34029\tval-rmse:0.36639\n", "[384]\ttrain-rmse:0.34003\tval-rmse:0.36619\n", "[385]\ttrain-rmse:0.33975\tval-rmse:0.36596\n", "[386]\ttrain-rmse:0.33948\tval-rmse:0.36579\n", "[387]\ttrain-rmse:0.33919\tval-rmse:0.36559\n", "[388]\ttrain-rmse:0.33895\tval-rmse:0.36539\n", "[389]\ttrain-rmse:0.33868\tval-rmse:0.36515\n", "[390]\ttrain-rmse:0.33844\tval-rmse:0.36495\n", "[391]\ttrain-rmse:0.33816\tval-rmse:0.36474\n", "[392]\ttrain-rmse:0.33790\tval-rmse:0.36458\n", "[393]\ttrain-rmse:0.33765\tval-rmse:0.36438\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[394]\ttrain-rmse:0.33740\tval-rmse:0.36419\n", "[395]\ttrain-rmse:0.33714\tval-rmse:0.36399\n", "[396]\ttrain-rmse:0.33689\tval-rmse:0.36380\n", "[397]\ttrain-rmse:0.33663\tval-rmse:0.36362\n", "[398]\ttrain-rmse:0.33637\tval-rmse:0.36342\n", "[399]\ttrain-rmse:0.33613\tval-rmse:0.36325\n", "[400]\ttrain-rmse:0.33587\tval-rmse:0.36304\n", "[401]\ttrain-rmse:0.33563\tval-rmse:0.36286\n", "[402]\ttrain-rmse:0.33537\tval-rmse:0.36267\n", "[403]\ttrain-rmse:0.33515\tval-rmse:0.36248\n", "[404]\ttrain-rmse:0.33488\tval-rmse:0.36227\n", "[405]\ttrain-rmse:0.33463\tval-rmse:0.36207\n", "[406]\ttrain-rmse:0.33436\tval-rmse:0.36187\n", "[407]\ttrain-rmse:0.33413\tval-rmse:0.36168\n", "[408]\ttrain-rmse:0.33389\tval-rmse:0.36151\n", "[409]\ttrain-rmse:0.33364\tval-rmse:0.36131\n", "[410]\ttrain-rmse:0.33340\tval-rmse:0.36115\n", "[411]\ttrain-rmse:0.33315\tval-rmse:0.36097\n", "[412]\ttrain-rmse:0.33288\tval-rmse:0.36078\n", "[413]\ttrain-rmse:0.33265\tval-rmse:0.36058\n", "[414]\ttrain-rmse:0.33241\tval-rmse:0.36038\n", "[415]\ttrain-rmse:0.33215\tval-rmse:0.36016\n", "[416]\ttrain-rmse:0.33191\tval-rmse:0.35999\n", "[417]\ttrain-rmse:0.33167\tval-rmse:0.35983\n", "[418]\ttrain-rmse:0.33142\tval-rmse:0.35966\n", "[419]\ttrain-rmse:0.33116\tval-rmse:0.35946\n", "[420]\ttrain-rmse:0.33092\tval-rmse:0.35927\n", "[421]\ttrain-rmse:0.33067\tval-rmse:0.35908\n", "[422]\ttrain-rmse:0.33043\tval-rmse:0.35890\n", "[423]\ttrain-rmse:0.33019\tval-rmse:0.35869\n", "[424]\ttrain-rmse:0.32994\tval-rmse:0.35849\n", "[425]\ttrain-rmse:0.32968\tval-rmse:0.35830\n", "[426]\ttrain-rmse:0.32943\tval-rmse:0.35813\n", "[427]\ttrain-rmse:0.32921\tval-rmse:0.35797\n", "[428]\ttrain-rmse:0.32896\tval-rmse:0.35778\n", "[429]\ttrain-rmse:0.32871\tval-rmse:0.35758\n", "[430]\ttrain-rmse:0.32848\tval-rmse:0.35738\n", "[431]\ttrain-rmse:0.32826\tval-rmse:0.35721\n", "[432]\ttrain-rmse:0.32802\tval-rmse:0.35703\n", "[433]\ttrain-rmse:0.32777\tval-rmse:0.35681\n", "[434]\ttrain-rmse:0.32752\tval-rmse:0.35661\n", "[435]\ttrain-rmse:0.32729\tval-rmse:0.35642\n", "[436]\ttrain-rmse:0.32707\tval-rmse:0.35624\n", "[437]\ttrain-rmse:0.32684\tval-rmse:0.35606\n", "[438]\ttrain-rmse:0.32658\tval-rmse:0.35590\n", "[439]\ttrain-rmse:0.32636\tval-rmse:0.35572\n", "[440]\ttrain-rmse:0.32611\tval-rmse:0.35555\n", "[441]\ttrain-rmse:0.32589\tval-rmse:0.35541\n", "[442]\ttrain-rmse:0.32564\tval-rmse:0.35521\n", "[443]\ttrain-rmse:0.32539\tval-rmse:0.35505\n", "[444]\ttrain-rmse:0.32514\tval-rmse:0.35491\n", "[445]\ttrain-rmse:0.32491\tval-rmse:0.35475\n", "[446]\ttrain-rmse:0.32467\tval-rmse:0.35458\n", "[447]\ttrain-rmse:0.32444\tval-rmse:0.35440\n", "[448]\ttrain-rmse:0.32421\tval-rmse:0.35425\n", "[449]\ttrain-rmse:0.32399\tval-rmse:0.35407\n", "[450]\ttrain-rmse:0.32376\tval-rmse:0.35391\n", "[451]\ttrain-rmse:0.32355\tval-rmse:0.35372\n", "[452]\ttrain-rmse:0.32330\tval-rmse:0.35353\n", "[453]\ttrain-rmse:0.32308\tval-rmse:0.35336\n", "[454]\ttrain-rmse:0.32287\tval-rmse:0.35321\n", "[455]\ttrain-rmse:0.32265\tval-rmse:0.35305\n", "[456]\ttrain-rmse:0.32241\tval-rmse:0.35289\n", "[457]\ttrain-rmse:0.32217\tval-rmse:0.35271\n", "[458]\ttrain-rmse:0.32193\tval-rmse:0.35253\n", "[459]\ttrain-rmse:0.32172\tval-rmse:0.35240\n", "[460]\ttrain-rmse:0.32149\tval-rmse:0.35222\n", "[461]\ttrain-rmse:0.32126\tval-rmse:0.35207\n", "[462]\ttrain-rmse:0.32103\tval-rmse:0.35190\n", "[463]\ttrain-rmse:0.32082\tval-rmse:0.35177\n", "[464]\ttrain-rmse:0.32058\tval-rmse:0.35159\n", "[465]\ttrain-rmse:0.32034\tval-rmse:0.35142\n", "[466]\ttrain-rmse:0.32016\tval-rmse:0.35126\n", "[467]\ttrain-rmse:0.31994\tval-rmse:0.35110\n", "[468]\ttrain-rmse:0.31972\tval-rmse:0.35093\n", "[469]\ttrain-rmse:0.31949\tval-rmse:0.35076\n", "[470]\ttrain-rmse:0.31928\tval-rmse:0.35057\n", "[471]\ttrain-rmse:0.31903\tval-rmse:0.35041\n", "[472]\ttrain-rmse:0.31880\tval-rmse:0.35025\n", "[473]\ttrain-rmse:0.31859\tval-rmse:0.35011\n", "[474]\ttrain-rmse:0.31836\tval-rmse:0.34993\n", "[475]\ttrain-rmse:0.31816\tval-rmse:0.34976\n", "[476]\ttrain-rmse:0.31793\tval-rmse:0.34959\n", "[477]\ttrain-rmse:0.31771\tval-rmse:0.34945\n", "[478]\ttrain-rmse:0.31749\tval-rmse:0.34930\n", "[479]\ttrain-rmse:0.31728\tval-rmse:0.34913\n", "[480]\ttrain-rmse:0.31705\tval-rmse:0.34896\n", "[481]\ttrain-rmse:0.31684\tval-rmse:0.34880\n", "[482]\ttrain-rmse:0.31663\tval-rmse:0.34864\n", "[483]\ttrain-rmse:0.31642\tval-rmse:0.34849\n", "[484]\ttrain-rmse:0.31620\tval-rmse:0.34831\n", "[485]\ttrain-rmse:0.31598\tval-rmse:0.34812\n", "[486]\ttrain-rmse:0.31575\tval-rmse:0.34799\n", "[487]\ttrain-rmse:0.31554\tval-rmse:0.34784\n", "[488]\ttrain-rmse:0.31532\tval-rmse:0.34767\n", "[489]\ttrain-rmse:0.31510\tval-rmse:0.34752\n", "[490]\ttrain-rmse:0.31488\tval-rmse:0.34733\n", "[491]\ttrain-rmse:0.31470\tval-rmse:0.34718\n", "[492]\ttrain-rmse:0.31449\tval-rmse:0.34704\n", "[493]\ttrain-rmse:0.31430\tval-rmse:0.34688\n", "[494]\ttrain-rmse:0.31411\tval-rmse:0.34672\n", "[495]\ttrain-rmse:0.31390\tval-rmse:0.34658\n", "[496]\ttrain-rmse:0.31368\tval-rmse:0.34640\n", "[497]\ttrain-rmse:0.31348\tval-rmse:0.34627\n", "[498]\ttrain-rmse:0.31330\tval-rmse:0.34614\n", "[499]\ttrain-rmse:0.31310\tval-rmse:0.34599\n", "best iteration 499\n", "Accuracy: 91.13%\n", "Error Rate: 8.87%\n", "xgboost success! \n", " cost time: 3.2673892974853516 (s)\n"]}], "source": ["#训练模型并保存  \n", "#xgb.train() 函数来训练 XGBoost 模型。参数分别为参数列表、训练数据.......\n", "model = xgb.train(  \n", "    plst,  \n", "    xgb_train,  \n", "    num_rounds,  \n", "    watchlist,\n", "    early_stopping_rounds=100,\n", ")      \n", "print(\"best iteration\", model.best_iteration)   #效果最好的迭代轮数\n", "  \n", "y_pred = model.predict(xgb_test)    #训练好的模型对测试集进行预测，返回的是预测的标签值。\n", "  \n", "# 计算预测准确率  \n", "#首先通过比较预测值和真实标签来计算正确预测的数量（填空）\n", "predicted_correctly = sum(1 for i in range(len(y_pred))if int(y_pred[i] > 0.5)  ==y_test[i])\n", "\n", "#然后将正确预测的数量除以总样本数量来得到准确率。                                           \n", "accuracy = predicted_correctly / float(len(y_pred))  \n", "\n", "print('Accuracy: %.2f%%' % (accuracy * 100.0))  \n", "  \n", "# 计算误差率  \n", "error_rate = sum(1 for i in range(len(y_pred)) if int(y_pred[i] > 0.5) != y_test[i]) / float(len(y_pred)) \n", "                    #将预测值与真实标签进行比较，计算错误预测的数量，并将其除以总样本数量来得到误差率。\n", "print('Error Rate: %.2f%%' % (error_rate * 100.0))  \n", "  \n", "# 输出运行时长  \n", "cost_time = time.time() - start_time  \n", "print(\"xgboost success!\", '\\n', \"cost time:\", cost_time, \"(s)\")"]}, {"cell_type": "markdown", "id": "caee89f1", "metadata": {}, "source": ["### 2.使用scikit-learn接口\n", "会改变的函数名是：\n", "\n", "eta -> learning_rate\n", "\n", "lambda -> reg_lambda\n", "\n", "alpha -> reg_alpha"]}, {"cell_type": "code", "execution_count": 10, "id": "dee9b457", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy : 0.9307\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn import metrics   #导入了 metrics 模块，用于评估模型的性能，例如计算准确率、精确率、召回率等指标。\n", "\n", "from xgboost import XGBClassifier #导入了 XGBClassifier 类，用于构建 XGBoost 分类器模型。\n", "\n", "clf = XGBClassifier(\n", "    learning_rate=0.3,  # 如同学习率\n", "    min_child_weight=1,\n", "    max_depth=6,  # 构建树的深度，越大越容易过拟合\n", "    gamma=0,  # 树的叶子节点上作进一步分区所需的最小损失减少,越大越保守，一般0.1、0.2这样子。\n", "    subsample=1,  # 随机采样训练样本 训练实例的子采样比\n", "    max_delta_step=0,  #最大增量步长，我们允许每个树的权重估计。\n", "    colsample_bytree=1,  # 生成树时进行的列采样 \n", "    reg_lambda=1,  # 控制模型复杂度的权重值的L2正则化项参数，参数越大，模型越不容易过拟合。\n", "    n_estimators=100,  #树的个数\n", "    seed=1000  #随机种子\n", ")\n", "clf.fit(X_train, y_train)\n", "\n", "y_true, y_pred = y_test, clf.predict(X_test)\n", "print(\"Accuracy : %.4g\" % metrics.accuracy_score(y_true, y_pred))"]}, {"cell_type": "markdown", "id": "0d02ed0f", "metadata": {}, "source": ["# 练习"]}, {"cell_type": "markdown", "id": "d323ea50", "metadata": {}, "source": ["首先，加载一个示例数据集（鸢尾花数据集），将数据集划分为训练集和测试集。接着，使用决策树模型作为基础模型，并将其作为参数传递给 Bagging 分类器。最后，对 Bagging 分类器进行训练，并在测试集上进行预测，计算模型的准确率。"]}, {"cell_type": "code", "execution_count": 1, "id": "480a51f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n"]}], "source": ["#导入需要的库鸢尾花数据、数据集划分、决策树分类器、bagging分类器、准确率函数\n", "from sklearn.datasets import load_iris\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.ensemble import BaggingClassifier\n", "from sklearn.metrics import accuracy_score\n", "#生成数据集鸢尾花\n", "data = load_iris()\n", "X, y =data.data,data.target\n", "#划分数据集为训练集与测试集8:2，结果可复现\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "#决策树模型初始化\n", "base_model = DecisionTreeClassifier()\n", "#初始化bagging分类器\n", "bagging_model = BaggingClassifier(base_model)\n", "#training\n", "bagging_model.fit(X_train, y_train)\n", "#进行预测\n", "y_pred = bagging_model.predict(X_test)\n", "#准确率\n", "\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(accuracy)"]}, {"cell_type": "code", "execution_count": null, "id": "b71fb2a0", "metadata": {}, "outputs": [], "source": ["#梯度下降"]}, {"cell_type": "code", "execution_count": 6, "id": "bc4f8f28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最终参数: [0.06815364 0.97681945]\n", "初始代价: 3.15109375\n", "最终代价: 0.0003878793258451959\n"]}], "source": ["import numpy as np\n", "\n", "\n", "\n", "def computeCost(X, y, theta):\n", "    m = len(y)\n", "    h = X @ theta\n", "    return np.sum((h - y) ** 2) / (2 * m)\n", "\n", "def gradientDescent(X, y, iterations, alpha):\n", "    # X:数据特征  y: 数据标签  iterations:迭代次数  alpha:学习率\n", "    # 取得数据总量（填空）\n", "    m = len(y)\n", "    # 初始化 theta 为 0，是一个 2 行 1 列的向量。（填空）\n", "    theta = 0\n", "    # 记录每次迭代的损失\n", "    costs = np.zeros(iterations)\n", "    # 初始化 theta 为 2 行 1 列的零向量\n", "    theta = np.zeros((X.shape[1], 1))\n", "    \n", "    for num in range(iterations):  \n", "        # 计算预测误差\n", "        error = X @ theta - y\n", "        # 梯度下降更新\n", "        theta = theta - alpha * (X.T @ error) / m\n", "        # 计算当前损失\n", "        costs[num] = computeCost(X, y, theta)\n", "    return theta, costs\n", "\n", "# 示例数据\n", "X = np.array([[1, 1], [1, 2], [1, 3], [1, 4]])  # 第一列是1，第二列是特征\n", "y = np.array([[1], [2], [3], [4]])  # 标签\n", "\n", "# 运行梯度下降\n", "theta, costs = gradientDescent(X, y, iterations=1000, alpha=0.01)\n", "\n", "print(\"最终参数:\", theta.ravel())\n", "print(\"初始代价:\", costs[0])\n", "print(\"最终代价:\", costs[-1])"]}, {"cell_type": "markdown", "id": "035a25f6", "metadata": {}, "source": ["最终参数: [1.56975904 0.53030637]\n", "初始代价: 2.937855234375\n", "最终代价: 0.21610256594678828"]}, {"cell_type": "code", "execution_count": null, "id": "c5f13aee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}