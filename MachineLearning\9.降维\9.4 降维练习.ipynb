{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习-降维"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sb\n", "from scipy.io import loadmat # 从scipy.io模块导入loadmat函数，用于加载MATLAB格式的文件"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 奇异值分解（Singular Value Decomposition）"]}, {"attachments": {"1749109138730.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![1749109138730.png](attachment:1749109138730.png)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 生成一个随机矩阵\n", "np.random.seed(42)  \n", "#使用np.random.rand函数生成5行3列的二维数组（填空）\n", "A = np.random.rand(5, 3)\n", "\n", "# 计算SVD\n", "U, S, VT = np.linalg.svd(A, full_matrices=True)  \n", "# 构造奇异值矩阵\n", "# 创建一个与矩阵U的列数和矩阵VT的行数相同的零矩阵Sigma（填空）\n", "Sigma = np.zeros((U.shape[1], VT.shape[0]))\n", "\n", "# 将奇异值将对角矩阵S嵌入到Sigma的前len(S)行和前len(S)列中（填空）\n", "Sigma[:len(S), :len(S)] = np.diag(S)\n", "\n", "# 根据公式重构原矩阵A（填空）\n", "A_reconstructed = np.dot(np.dot(U, Sigma), VT)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原矩阵A:\n", "[[0.37454012 0.95071431 0.73199394]\n", " [0.59865848 0.15601864 0.15599452]\n", " [0.05808361 0.86617615 0.60111501]\n", " [0.70807258 0.02058449 0.96990985]\n", " [0.83244264 0.21233911 0.18182497]]\n", "\n", "左奇异矩阵U:\n", "[[-0.5991048  -0.38620771 -0.12988737 -0.68883081 -0.02363108]\n", " [-0.25170251  0.32375656 -0.38389036  0.13776803 -0.81576694]\n", " [-0.4495347  -0.55516825  0.01152904  0.69900869  0.03099514]\n", " [-0.51180949  0.4814656   0.71001691  0.04057048  0.0217244 ]\n", " [-0.33717783  0.45387706 -0.57576083  0.12756552  0.57665694]]\n", "\n", "奇异值矩阵Sigma:\n", "[[1.99063285 0.         0.        ]\n", " [0.         1.0096001  0.        ]\n", " [0.         0.         0.57767497]\n", " [0.         0.         0.        ]\n", " [0.         0.         0.        ]]\n", "\n", "右奇异矩阵VT:\n", "[[-0.52458829 -0.54271957 -0.65594405]\n", " [ 0.72866708 -0.6846751  -0.01625695]\n", " [-0.44028559 -0.48649304  0.75463443]]\n", "\n", "重构的原矩阵A_reconstructed:\n", "[[0.37454012 0.95071431 0.73199394]\n", " [0.59865848 0.15601864 0.15599452]\n", " [0.05808361 0.86617615 0.60111501]\n", " [0.70807258 0.02058449 0.96990985]\n", " [0.83244264 0.21233911 0.18182497]]\n", "\n", "重构矩阵与原矩阵是否相同: True\n"]}], "source": ["# 打印结果\n", "print(\"原矩阵A:\")\n", "print(A)\n", "print(\"\\n左奇异矩阵U:\")\n", "print(U)\n", "print(\"\\n奇异值矩阵Sigma:\")\n", "print(Sigma)\n", "print(\"\\n右奇异矩阵VT:\")\n", "print(VT)\n", "print(\"\\n重构的原矩阵A_reconstructed:\")\n", "print(A_reconstructed)\n", "\n", "# 验证重构矩阵是否与原矩阵相同，使用NumPy库的allclose函数来检查两个矩阵是否接近相等\n", "print(\"\\n重构矩阵与原矩阵是否相同:\", np.allclose(A, A_reconstructed))  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Principal component analysis（主成分分析）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PCA是在数据集中找到“主成分”或最大方差方向的线性变换。 它可以用于降维。 在本练习中，我们首先负责实现PCA并将其应用于一个简单的二维数据集，以了解它是如何工作的。 我们从加载和可视化数据集开始。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>X1</th>\n", "      <th>X2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3.381563</td>\n", "      <td>3.389113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.527875</td>\n", "      <td>5.854178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2.655682</td>\n", "      <td>4.411995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2.765235</td>\n", "      <td>3.715414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.846560</td>\n", "      <td>4.175506</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         X1        X2\n", "0  3.381563  3.389113\n", "1  4.527875  5.854178\n", "2  2.655682  4.411995\n", "3  2.765235  3.715414\n", "4  2.846560  4.175506"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用pandas读取csv文件pcadata（填空）\n", "data = pd.read_csv('data/pcadata.csv')\n", "data.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#将 data 转换为 NumPy 数组 X\n", "X = data.values"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "# 在二维坐标系中绘制了 X 的第一列（即第一个特征）和第二列（即第二个特征）的值\n", "ax.scatter(X[:, 0], X[:, 1])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PCA的算法相当简单。 在确保数据被归一化之后，输出仅仅是原始数据的协方差矩阵的奇异值分解。这段代码实现了主成分分析（PCA）算法，其任务是从输入数据集中提取主成分。具体来说，PCA通过对数据进行线性变换，将数据投影到一个新的坐标系中，使得新的坐标系的基向量（即主成分）按照数据的方差最大化的方向排列。这样可以用较少的主成分来描述数据的主要特征，从而实现降维和去噪。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 已知输入数据为X\n", "def pca(X):\n", "    \"\"\"\n", "    执行主成分分析（PCA）来提取数据集X的主成分。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        输入数据矩阵，假设每一行是一个数据样本，每一列是一个特征。\n", "        \n", "    返回:\n", "    U : numpy.ndarray\n", "        主成分方向（特征向量）。\n", "    S : numpy.n<PERSON><PERSON>\n", "        奇异值（对应于主成分方向的特征值）。\n", "    V : numpy.n<PERSON><PERSON>\n", "        V矩阵，用于SVD分解中的右奇异矩阵。\n", "    \"\"\"\n", "    \n", "    # 对输入数据减去均值并除以标准差\n", "    X = (X - X.mean(axis=0)) / X.std(axis=0)\n", "    \n", "    # 将标准化后的数据使用np.matrix转换为矩阵形式\n", "    X = np.matrix(X)\n", "    \n", "    # 计算协方差矩阵表示各特征之间的线性关系\n", "    # 协方差矩阵的计算方式是先将数据矩阵转置，然后与自身相乘，再除以样本数\n", "    m = X.shape[0]\n", "    cov = (X.T * X) / m\n", "    \n", "    # 对协方差矩阵进行奇异值分解（SVD）\n", "    U, S, V = np.linalg.svd(cov)\n", "    \n", "    return U, S, V\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(matrix([[-0.70710678, -0.70710678],\n", "         [-0.70710678,  0.70710678]]),\n", " array([1.73553038, 0.26446962]),\n", " matrix([[-0.70710678, -0.70710678],\n", "         [-0.70710678,  0.70710678]]))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["U, S, V = pca(X)\n", "U, S, V"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们有主成分（矩阵U），我们可以用这些来将原始数据投影到一个较低维的空间中。 对于这个任务，我们将实现一个计算投影并且仅选择顶部K个分量的函数，有效地减少了维数。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def project_data(X, U, k):\n", "    \"\"\"\n", "    将原始数据X投影到由主成分矩阵U的前k个特征向量构成的子空间中。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        原始数据矩阵，每行代表一个样本，每列代表一个特征。\n", "    U : numpy.ndarray\n", "        主成分矩阵，每列是一个主成分（特征向量）。\n", "    k : int\n", "        要使用的主成分数量。\n", "    \n", "    返回:\n", "    projected_data : numpy.ndarray\n", "        投影后的数据，维度为 (m, k)，其中 m 是样本数量。\n", "    \"\"\"\n", "    \n", "    # 从主成分矩阵U中选择前k个主成分（填空）\n", "    U_reduced = U[:, :k]\n", "    \n", "    # 将原始数据投影到选定的主成分空间中， 使用矩阵乘法来进行投影（填空）\n", "    projected_data = np.dot(X, U_reduced)\n", "    \n", "    return projected_data\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-4.78759045],\n", "        [-7.34122042],\n", "        [-4.99760204],\n", "        [-4.58251038],\n", "        [-4.96535088],\n", "        [-7.33909864],\n", "        [-5.02657745],\n", "        [-8.90393595],\n", "        [-6.38329374],\n", "        [-7.20197778],\n", "        [-7.05107253],\n", "        [-6.95290959],\n", "        [-6.4674726 ],\n", "        [-7.00429773],\n", "        [-4.66963233],\n", "        [-8.4480763 ],\n", "        [-7.3196659 ],\n", "        [-5.28701594],\n", "        [-8.81999314],\n", "        [-6.95020407],\n", "        [-8.75989905],\n", "        [-5.92729807],\n", "        [-8.315444  ],\n", "        [-6.33065452],\n", "        [-4.54033809],\n", "        [-5.77450612],\n", "        [-7.69650922],\n", "        [-7.70841907],\n", "        [-5.39933015],\n", "        [-6.35631705],\n", "        [-6.53101334],\n", "        [-8.06183103],\n", "        [-4.92218554],\n", "        [-7.0797285 ],\n", "        [-5.61499734],\n", "        [-7.8001156 ],\n", "        [-4.53023245],\n", "        [-7.90528254],\n", "        [-3.56709632],\n", "        [-6.67057627],\n", "        [-7.38689297],\n", "        [-5.3854378 ],\n", "        [-4.95846894],\n", "        [-7.41694217],\n", "        [-4.429343  ],\n", "        [-6.63230794],\n", "        [-2.87009502],\n", "        [-4.94269685],\n", "        [-5.98252923],\n", "        [-7.92173768]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["Z = project_data(X, U, 1)\n", "Z"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以通过反向转换步骤来恢复原始数据。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def recover_data(Z, U, k):\n", "    \"\"\"\n", "    将投影后的数据Z恢复到原始特征空间中。\n", "    \n", "    参数:\n", "    Z : numpy.n<PERSON><PERSON>\n", "        投影后的数据矩阵，维度为 (m, k)，其中 m 是样本数量，k 是投影到的主成分数量。\n", "    U : numpy.ndarray\n", "        主成分矩阵，每列是一个主成分（特征向量）。\n", "    k : int\n", "        使用的主成分数量。\n", "    \n", "    返回:\n", "    recovered_data : numpy.ndarray\n", "        恢复到原始特征空间的数据矩阵，维度与原始数据X相同。\n", "    \"\"\"\n", "    \n", "    # 从主成分矩阵U中选择前k个主成分\n", "    U_reduced = U[:,:k]\n", "    \n", "    # 使用矩阵乘法将投影后的数据恢复到原始特征空间\n", "    # 这里使用U_reduced的转置，因为投影过程中是使用U_reduced而不是U\n", "    recovered_data = np.dot(Z, U_reduced.T)\n", "    \n", "    return recovered_data\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[3.38533768, 3.38533768],\n", "        [5.19102674, 5.19102674],\n", "        [3.53383829, 3.53383829],\n", "        [3.24032416, 3.24032416],\n", "        [3.51103328, 3.51103328],\n", "        [5.18952641, 5.18952641],\n", "        [3.554327  , 3.554327  ],\n", "        [6.29603349, 6.29603349],\n", "        [4.51367029, 4.51367029],\n", "        [5.09256733, 5.09256733],\n", "        [4.9858612 , 4.9858612 ],\n", "        [4.91644952, 4.91644952],\n", "        [4.57319373, 4.57319373],\n", "        [4.95278642, 4.95278642],\n", "        [3.30192868, 3.30192868],\n", "        [5.97369204, 5.97369204],\n", "        [5.17578539, 5.17578539],\n", "        [3.73848482, 3.73848482],\n", "        [6.23667696, 6.23667696],\n", "        [4.91453643, 4.91453643],\n", "        [6.19418402, 6.19418402],\n", "        [4.19123266, 4.19123266],\n", "        [5.87990684, 5.87990684],\n", "        [4.47644874, 4.47644874],\n", "        [3.21050385, 3.21050385],\n", "        [4.08319244, 4.08319244],\n", "        [5.44225386, 5.44225386],\n", "        [5.4506754 , 5.4506754 ],\n", "        [3.81790296, 3.81790296],\n", "        [4.49459489, 4.49459489],\n", "        [4.61812382, 4.61812382],\n", "        [5.70057539, 5.70057539],\n", "        [3.48051077, 3.48051077],\n", "        [5.00612403, 5.00612403],\n", "        [3.97040269, 3.97040269],\n", "        [5.51551464, 5.51551464],\n", "        [3.20335809, 3.20335809],\n", "        [5.58987889, 5.58987889],\n", "        [2.522318  , 2.522318  ],\n", "        [4.71680971, 4.71680971],\n", "        [5.22332211, 5.22332211],\n", "        [3.80807959, 3.80807959],\n", "        [3.50616701, 3.50616701],\n", "        [5.2445701 , 5.2445701 ],\n", "        [3.13201847, 3.13201847],\n", "        [4.68974992, 4.68974992],\n", "        [2.02946365, 2.02946365],\n", "        [3.49501446, 3.49501446],\n", "        [4.23028698, 4.23028698],\n", "        [5.60151443, 5.60151443]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["X_recovered = recover_data(Z, U, 1)\n", "X_recovered"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(list(X_recovered[:, 0]), list(X_recovered[:, 1]))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请注意，第一主成分的投影轴基本上是数据集中的对角线。 当我们将数据减少到一个维度时，我们失去了该对角线周围的变化，所以在我们的再现中，一切都沿着该对角线。\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们在此练习中的最后一个任务是将PCA应用于脸部图像。 通过使用相同的降维技术，我们可以使用比原始图像少得多的数据来捕获图像的“本质”。这段代码加载了名为 \"ex7faces.mat\" 的MATLAB文件，其中包含了一个名为 'X' 的变量。这个变量可能是一个矩阵，代表着一组人脸图像数据。然后，它将加载的数据存储在变量 X 中，并打印出了 X 的形状。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5000, 1024)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["faces = loadmat('data/ex7faces.mat')\n", "X = faces['X']\n", "X.shape"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def plot_n_image(X, n):\n", "    \"\"\"\n", "    绘制前n个图像。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        包含图像数据的矩阵，每行是一个图像，每列是一个像素。\n", "    n : int\n", "        要绘制的图像数量。n必须是一个平方数。\n", "    \"\"\"\n", "    \n", "    # 计算每个图像的大小（假设图像是正方形）\n", "    pic_size = int(np.sqrt(X.shape[1]))\n", "    \n", "    # 计算要绘制的图像在网格中的行数和列数\n", "    grid_size = int(np.sqrt(n))\n", "\n", "    # 从输入数据中选择前n个图像（填空）\n", "    first_n_images = X[:n]\n", "\n", "    # 创建一个具有多个子图的网格\n", "    fig, ax_array = plt.subplots(nrows=grid_size,\n", "                                 ncols=grid_size,\n", "                                 sharey=True,\n", "                                 sharex=True,\n", "                                 figsize=(8, 8))\n", "\n", "    # 循环遍历网格中的每个子图，并在每个子图中绘制一个图像\n", "    for r in range(grid_size):\n", "        for c in range(grid_size):\n", "            # 显示图像\n", "            ax_array[r, c].imshow(first_n_images[grid_size * r + c].reshape((pic_size, pic_size)))\n", "            # 去除坐标轴刻度\n", "            plt.xticks(np.array([]))\n", "            plt.yticks(np.array([]))\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# 注意：X[3, :] 选择的是第四行数据，其中32*32 = 1024是该数据的长度\n", "face = np.reshape(X[3,:], (32, 32))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(face) ## 使用imshow函数显示人脸图像\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看起来很糟糕。 这些只有32 x 32灰度的图像（它也是侧面渲染，但我们现在可以忽略）。 我们的下一步是在面数据集上运行PCA，并取得前100个主要特征。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["U, S, V = pca(X)\n", "Z = project_data(X, U, 100)  #将原始数据X投影到由主成分矩阵U的前k个特征向量构成的子空间中。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们可以尝试恢复原来的结构并再次渲染。"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X_recovered = recover_data(Z, U, 100)\n", "face = np.reshape(X_recovered[3,:], (32, 32))\n", "plt.imshow(face)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以看到：数据维度减少，但细节并没有怎么损失。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["练习：定义一个 pca() 函数，该函数实现了主成分分析（PCA）算法。然后，生成了一个虚拟的数据集 X，其中包含100个样本和3个特征。接下来，调用 pca() 函数对数据进行降维，保留了2个主成分。最后，输出原始数据矩阵和降维后的数据矩阵的形状。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["\n", "def pca(X, k):\n", "    \"\"\"\n", "    使用主成分分析（PCA）对数据集进行降维和特征提取。\n", "    \n", "    参数:\n", "    X : numpy.n<PERSON>ray\n", "        输入数据矩阵，每行代表一个样本，每列代表一个特征。\n", "    k : int\n", "        要保留的主成分数量。\n", "    \n", "    返回:\n", "    X_reduced : numpy.n<PERSON><PERSON>\n", "        降维后的数据矩阵，每行代表一个样本，每列代表一个特征（主成分）。\n", "    \"\"\"\n", "    # 标准化数据\n", "    # 列方向计算均值axis=0（填空）\n", "    X_mean = np.mean(X, axis=0)\n", "    #标准差\n", "    X_std = np.std(X, axis=0)\n", "    # 将原始数据矩阵X的每个元素减去对应特征的均值，再除以对应特征的标准差，实现数据标准化（填空）\n", "    X_normalized = (X - X_mean) / X_std\n", "    \n", "    # 计算协方差矩阵\n", "    cov_matrix = np.cov(X_normalized, rowvar=False)\n", "    \n", "    # 对协方差矩阵进行奇异值分解（填空）\n", "    U, S, V = np.linalg.svd(cov_matrix)\n", "    \n", "    # 选择前k个主成分\n", "    U_reduced = U[:, :k]\n", "    \n", "    # 使用主成分矩阵将数据投影到新的低维空间中（填空）\n", "    X_reduced = np.dot(X_normalized, U_reduced)\n", "    \n", "    return X_reduced\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据矩阵形状: (100, 3)\n", "降维后的数据矩阵形状: (100, 2)\n"]}], "source": ["# 生成一个虚拟数据集\n", "np.random.seed(0)\n", "X = np.random.rand(100, 3)  # 100个样本，每个样本3个特征\n", "\n", "# 调用PCA函数对数据进行降维\n", "k = 2  # 保留2个主成分\n", "X_reduced = pca(X, k)\n", "\n", "print(\"原始数据矩阵形状:\", X.shape)\n", "print(\"降维后的数据矩阵形状:\", X_reduced.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 1}