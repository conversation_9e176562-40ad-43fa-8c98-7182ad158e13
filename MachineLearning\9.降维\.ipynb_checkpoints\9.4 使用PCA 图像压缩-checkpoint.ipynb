import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from PIL import Image  

# 步骤 1: 读取图像数据
image = Image.open('a.png').convert('L')  # 以灰度模式读取图像
image_array = np.array(image) / 255.0  # 将像素值归一化到 [0, 1]

# 获取图像的形状
original_shape = image_array.shape

# 将图像展平，每行表示一行像素
X = image_array.reshape(-1, original_shape[1])

# 步骤 2: 数据标准化（中心化）
mean_image = np.mean(X, axis=0)
X_centered = X - mean_image

# 步骤 3: 计算协方差矩阵并进行PCA
k = 50  # 选择要保留的主成分数
pca = PCA(n_components=k)
X_pca = pca.fit_transform(X_centered)  # 转换到主成分空间

# 步骤 6: 数据投影（还原）
X_reconstructed = pca.inverse_transform(X_pca)

# 步骤 7: 图像重构
reconstructed_image = X_reconstructed + mean_image
reconstructed_image = reconstructed_image.reshape(original_shape)

# 计算图片的原始大小和压缩后的大小
original_size = X_centered.size
compressed_size = X_pca.size + k * X_centered.shape[1]  # 压缩数据加上PCA逆变换的特征数

# 计算压缩比率
compression_ratio = compressed_size / original_size

# 显示图片前后的大小和压缩比率
print(f"Original size: {original_size} pixels")
print(f"Compressed size: {compressed_size} pixels")
print(f"Compression ratio: {compression_ratio:.2f}")

# 步骤 8: 可视化结果
plt.figure(figsize=(10, 5))

# 原始图像
plt.subplot(1, 2, 1)
plt.imshow(image_array, cmap='gray')
plt.title('Original Image')
plt.axis('off')

# 重构图像
plt.subplot(1, 2, 2)
plt.imshow(reconstructed_image, cmap='gray')
plt.title(f'Reconstructed Image with {k} components')
plt.axis('off')

plt.show()

