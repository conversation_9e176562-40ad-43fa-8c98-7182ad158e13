{"cells": [{"cell_type": "markdown", "id": "aae39764", "metadata": {}, "source": ["# ID3代码实现"]}, {"cell_type": "markdown", "id": "e6804de2", "metadata": {}, "source": ["## 信息熵表达式如下"]}, {"attachments": {"1746689076117.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "9657f05a", "metadata": {}, "source": ["![1746689076117.png](attachment:1746689076117.png)"]}, {"cell_type": "code", "execution_count": 1, "id": "508207ef", "metadata": {}, "outputs": [], "source": ["import math\n", "from collections import Counter\n", "\n", "\n", "# 数据集\n", "data = [\n", "    {\"温度\": 1, \"起风\": 0, \"下雨\": 0, \"湿度\": 1, \"外出\": 1},\n", "    {\"温度\": 0, \"起风\": 0, \"下雨\": 1, \"湿度\": 1, \"外出\": 1},\n", "    {\"温度\": 0, \"起风\": 1, \"下雨\": 0, \"湿度\": 0, \"外出\": 0},\n", "    {\"温度\": 1, \"起风\": 1, \"下雨\": 0, \"湿度\": 0, \"外出\": 1},\n", "    {\"温度\": 1, \"起风\": 0, \"下雨\": 0, \"湿度\": 0, \"外出\": 1},\n", "    {\"温度\": 1, \"起风\": 1, \"下雨\": 0, \"湿度\": 0, \"外出\": 1},\n", "]"]}, {"cell_type": "code", "execution_count": 2, "id": "e784dc10", "metadata": {}, "outputs": [], "source": ["# 属性列表\n", "attributes = [\"温度\", \"起风\", \"下雨\", \"湿度\"]\n", "# 目标属性\n", "target_attr = \"外出\""]}, {"cell_type": "markdown", "id": "4f941f6b", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 3, "id": "5df45d5f", "metadata": {}, "outputs": [], "source": ["# 计算数据集的熵（补充完整代码）\n", "def entropy(data, target_attr):\n", "    # 从数据集中提取目标属性的值\n", "    values = [record[target_attr] for record in data]\n", "    # 统计每个目标属性值出现的次数\n", "    nums = Counter(values)\n", "    # 初始化熵值为 0\n", "    entropy = 0.0\n", "    # 遍历每个目标属性值的出现次数\n", "    for count in nums.values():\n", "        # 计算该值出现的概率\n", "        pro = count / len(data)\n", "        # 根据熵的计算公式更新熵值\n", "        entropy -= pro * math.log2(pro)\n", "    return entropy"]}, {"cell_type": "code", "execution_count": 4, "id": "1724b09b", "metadata": {}, "outputs": [], "source": ["# 计算信息增益\n", "def information_gain(data, split_attr, target_attr):\n", "    total_entropy = entropy(data, target_attr)\n", "    value_counts = Counter([record[split_attr] for record in data])\n", "    split_entropy = 0.0\n", "    for value, count in value_counts.items():\n", "        subset = [record for record in data if record[split_attr] == value]\n", "        probability = count / len(data)\n", "        split_entropy += probability * entropy(subset, target_attr)\n", "    return total_entropy - split_entropy\n", "\n", "# 选择最佳分裂属性\n", "def best_attribute(data, attributes, target_attr):\n", "    gains = [(attr, information_gain(data, attr, target_attr)) for attr in attributes]\n", "    return max(gains, key=lambda x: x[1])[0]\n", "\n", "# 返回数据集中出现最多的类别\n", "def majority_class(data, target_attr):\n", "    values = [record[target_attr] for record in data]\n", "    return Counter(values).most_common(1)[0][0]"]}, {"cell_type": "code", "execution_count": 7, "id": "ff21e57b", "metadata": {}, "outputs": [], "source": ["# ID3算法构建决策树（补全代码）\n", "def id3(data, attributes, target_attr):\n", "    # 从数据集中提取目标属性的值\n", "    values = [record[target_attr] for record in data]\n", "    # 如果所有实例的目标属性值都相同，返回该值作为叶子节点\n", "    if len(set(values)) == 1:\n", "        return values[0]\n", "    # 如果没有更多属性可用于分裂，返回出现次数最多的目标属性值作为叶子节点\n", "    if not attributes:\n", "        return majority_class(data, target_attr)\n", "        \n", "    # 选择最佳分裂属性\n", "    best_attr = best_attribute(data, attributes, target_attr)\n", "    \n", "    # 初始化决策树，以最佳属性为根节点\n", "    tree = {best_attr: {}}\n", "    \n", "    # 移除最佳属性，得到剩余属性列表\n", "    remaining_attrs = [attr for attr in attributes if attr != best_attr]\n", "\n", "    # 对最佳属性的每个可能值创建子树\n", "    for value in set(record[best_attr] for record in data):\n", "        subset = [record for record in data if record[best_attr] == value]\n", "        # 如果子集为空，返回出现次数最多的目标属性值作为叶子节点\n", "        if not subset:\n", "            tree[best_attr][value] = majority_class(data, target_attr)\n", "        else:\n", "            subtree = id3(subset, remaining_attrs, target_attr)\n", "            tree[best_attr][value] = subtree\n", "\n", "    return tree"]}, {"cell_type": "code", "execution_count": 8, "id": "dbdbc22c", "metadata": {}, "outputs": [], "source": ["# 打印树结构\n", "def print_tree(tree, depth=0):\n", "    if isinstance(tree, dict):\n", "        for attr, branches in tree.items():\n", "            for value, subtree in branches.items():\n", "                print(\" \" * depth * 2 + f\"{attr} = {value}:\")\n", "                print_tree(subtree, depth + 1)\n", "    else:\n", "        print(\" \" * depth * 2 + f\"外出 = {tree}\")\n", "\n", "\n", "# 生成决策树\n", "tree = id3(data, attributes, target_attr)"]}, {"cell_type": "code", "execution_count": 9, "id": "b9779a4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["温度 = 0:\n", "  起风 = 0:\n", "    外出 = 1\n", "  起风 = 1:\n", "    外出 = 0\n", "温度 = 1:\n", "  外出 = 1\n"]}], "source": ["# 打印决策树\n", "print_tree(tree)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}