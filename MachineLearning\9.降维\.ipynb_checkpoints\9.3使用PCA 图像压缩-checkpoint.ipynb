import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from PIL import Image  

# 步骤 1: 读取图像数据
# Image.open打开图像，并使用convert函数转换为灰度模式L（表示Luminance，即灰度）（填空）
image = pass
 # 将图像转为NumPy数组，并将像素值归一化到[0, 1]范围（原值范围0-255），所以除以255.0（填空）
image_array = pass

# 获取图像的形状
original_shape = image_array.shape

# 将图像展平，每行表示一行像素
X = image_array.reshape(-1, original_shape[1])

# 步骤 2: 数据标准化（中心化），列方向计算均值axis=0（填空）
mean_image = pass
X_centered = pass

# 步骤 3: 计算协方差矩阵并进行PCA，选择要保留的主成分数
k = 50  
# 初始化PCA模型，指定保留k个主成分（填空）
pca = pass
X_pca = pca.fit_transform(X_centered)  # 拟合并转换到主成分空间

# 步骤 6: # 将降维后的数据反向投影回原始空间（近似重建）
X_reconstructed = pca.inverse_transform(X_pca)

# 步骤 7: 图像重构，加回均值，恢复原始数据范围
reconstructed_image = X_reconstructed + mean_image
reconstructed_image = reconstructed_image.reshape(original_shape)

# 计算图片的原始大小和压缩后的大小
original_size = X_centered.size
compressed_size = X_pca.size + k * X_centered.shape[1]  # 压缩数据加上PCA逆变换的特征数

# 计算压缩比率，压缩后大小/原始大小（填空）
compression_ratio = pass

# 显示图片前后的大小和压缩比率
print(f"Original size: {original_size} pixels")
print(f"Compressed size: {compressed_size} pixels")
print(f"Compression ratio: {compression_ratio:.2f}")

# 步骤 8: 可视化结果
plt.figure(figsize=(10, 5))

# 原始图像
plt.subplot(1, 2, 1)
plt.imshow(image_array, cmap='gray')
plt.title('Original Image')
plt.axis('off')

# 重构图像
plt.subplot(1, 2, 2)
plt.imshow(reconstructed_image, cmap='gray')
plt.title(f'Reconstructed Image with {k} components')
plt.axis('off')

plt.show()

