{"cells": [{"cell_type": "markdown", "id": "859ffe7f", "metadata": {}, "source": ["# 奇异值分解"]}, {"attachments": {"1748503655048.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAF4AAAAkCAYAAAAAa43JAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAIDSURBVGhD7ZZBloIwDIa5FR5HOQ3P9RxDn0dh4UnGhduatqSENhWUMJlFFt/TB7UNX/8Gm9/H0xl/j4lXwsQrYeKVMPFKmHglTLwSJl4JE6+EiVfCxK/h2rmmaTI6d+HGrsTEr2DoO9cP/vPgmvbshsfNHcMnP34NJn41d9e3kPTTjbn3OeLiQyoEjuJbhrNrybFv+3u4Pq09XfNcTtPYnONPZS7SXuI1SDl8P16zWr5EWHwsLha8o3ggyaRHHjcEUjlrA0kiqSmMPYQWkuaapXl8Fpw/zBHHT2O+R1R8eIBTN8qXK5IDZdFko+AylWObmN3zYuNGpJNCxWei43pyYZITnxKEqd9TPL9GFMivm+QyL8VSfNyotKmktf2zVkMLXRI/pa/OwqYlETSBOG8llek35dyFeJ/2jf9alpARH44lPjAKWJC3AbY14IbPrlHgPk0xZWxR8bexfqlk1xAQjwkv2at4tr+PiWbFzk5kfg+g4v336ubJsVl8TB935Gvip/t13p0Wfv5YB/c7P/7AbBKpOYmHPwYwdq+TStkmnk3ZkvjtFInH/l0k9c0m0x6O4oHqqRBmg3jSYshDoJTi4US5wzpjnw9AokF6vtaslozyBPjrlRfzDsi8XI2PMfFKmHglTLwSJl4JE6+EiVfCxCth4pUw8UqYeCVMvBImXoWnewGHt8mIwjkKugAAAABJRU5ErkJggg=="}}, "cell_type": "markdown", "id": "6e0c5ce4", "metadata": {}, "source": ["![1748503655048.png](attachment:1748503655048.png)"]}, {"attachments": {"1748503601096.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAFcAAAA0CAYAAAD/qKSYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAIKSURBVHhe7ZY9coMwEEa5FRzHcBqGOsfA46NQ+CRx4VaRgNW/sEL4Un3Fm8lIIlq9Xa3cfL/eimCgXCCUC4RygVAuEMoFQrlAKBcI5QKhXCCUC4RygVAuEMoFQrlAKBcI5QKhXCCUC4RygVAuEMoFQrlAKLfIU81jp2733FwdlJvFiJ3U2F8td5lU2zSq2WnH5zq+6CzGY0jcfoOaZbwU231IxuberYupFWZiuLxybWDtpBYZl4P1DzcG46FuVoYnV2Nj03Ek6/14rfA4OZ0av1wyAvzvNVC5QYXuwf5ls1rW/fthF6xlLG7OVrQvd40tXGeu9thu53AxmySEyToCIFeqJneo+AAApLqWozh8uZvEXKuya6OKrOV6ubav+RmWKihl3VVJmZrE+KIq5ZqqLcmzZ/ltUeg49GMmsbdBC6onkZtWhmE/6MlNqlmvtyRQEhaJkV66xrKtKVeXjtsmKzePJZGb7bd7BWCDlEpNCeT5cs3fxYT7tyA3jyeSK9USHmir5qOr5b4rc3w1tz1yragkVz94bel/mm+7TIHUP2ZX8LlypW8hW0L2ZnyQm6wXDhJ98mE7S/qg6eBmr5mbijMNHReU1w68w9vfs9G4e6TyVRh8F/HfLSIjl1wF5QKhXCCUC4RygVAuEMoFQrlAKBcI5QKhXCCUC4RygVAuEMoFQrlAKBfGW/0Af5TsmjUUv6kAAAAASUVORK5CYII="}}, "cell_type": "markdown", "id": "7b955b80", "metadata": {}, "source": ["![1748503601096.png](attachment:1748503601096.png)"]}, {"cell_type": "code", "execution_count": 1, "id": "cfbb0a0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始矩阵 A:\n", " [[ 1  2]\n", " [ 4  8]\n", " [ 7 10]]\n", "A^T * A:\n", " [[ 66 104]\n", " [104 168]]\n", "A^T * A 的特征值:\n", " [232.83177457   1.16822543]\n", "右奇异向量矩阵 V:\n", " [[ 0.52901149 -0.84861466]\n", " [ 0.84861466  0.52901149]]\n", "奇异值 Sigma:\n", " [15.25882612  1.08084478]\n", "Sigma 矩阵:\n", " [[15.25882612  0.        ]\n", " [ 0.          1.08084478]\n", " [ 0.          0.        ]]\n", "左奇异向量矩阵 U:\n", " [[ 0.14589856  0.19374503]\n", " [ 0.58359425  0.77498014]\n", " [ 0.79883124 -0.60155519]]\n"]}], "source": ["import numpy as np\n", "\n", "# 定义矩阵 A\n", "A = np.array([[1, 2], [4, 8], [7, 10]])\n", "\n", "# 步骤 1：计算 A^T * A\n", "# 计算 A 的转置，然后与 A 相乘，得到协方差矩阵 A_T_A(填空)\n", "A_T_A = pass\n", "\n", "# 步骤 2：对 A^T * A 进行特征值分解\n", "# 使用 numpy 的 linalg.eigh 函数对对称矩阵 A_T_A 进行特征值分解(填空)\n", "eigvals, V = pass\n", "\n", "# 将特征值和特征向量按降序排序\n", "# 使用numpy中的argsort 返回从小到大的索引，我们通过 [::-1] 反转为从大到小(填空)\n", "sorted_indices = pass\n", "## 排序后的特征值（填空）\n", "eigvals = pass  \n", "V = V[:, sorted_indices]  # 对应排序后的特征向量\n", "\n", "# 步骤 3：计算奇异值 (Sigma)\n", "# 奇异值是特征值的平方根（保留非负性）（填空）\n", "Sigma = pass  \n", "\n", "# 构建全零奇异值矩阵 (3x2) 以适应原始矩阵 A 的形状（填空）\n", "Sigma_matrix = pass\n", "#主对角线元素为奇异值，其余为0，将奇异值填入对角线\n", "np.fill_diagonal(pass)  \n", "\n", "# 步骤 4：计算左奇异向量矩阵 U\n", "# U = A * V * Sigma^(-1)\n", "# 计算 Sigma 的逆，只考虑非零部分 (前 2x2)（填空）\n", "Sigma_inv = np.linalg.inv(pass)# 计算 U 矩阵\n", "# 根据公式，计算 U 矩阵（填空）\n", "U = pass\n", "\n", "# 对 U 的每一列进行归一化处理，确保其为单位向量\n", "U[:, 0] = U[:, 0] / np.linalg.norm(U[:, 0])  # 归一化第一列\n", "U[:, 1] = U[:, 1] / np.linalg.norm(U[:, 1])  # 归一化第二列\n", "\n", "# 显示计算结果\n", "print(\"原始矩阵 A:\\n\", A)\n", "print(\"A^T * A:\\n\", A_T_A)\n", "print(\"A^T * A 的特征值:\\n\", eigvals)\n", "print(\"右奇异向量矩阵 V:\\n\", V)\n", "print(\"奇异值 Sigma:\\n\", Sigma)\n", "print(\"Sigma 矩阵:\\n\", Sigma_matrix)\n", "print(\"左奇异向量矩阵 U:\\n\", U)\n"]}, {"cell_type": "markdown", "id": "dfc98fcb", "metadata": {}, "source": ["# 基于数据矩阵的奇异值分解（SVD）的PCA实现"]}, {"cell_type": "code", "execution_count": 1, "id": "cd98dbb4", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题\n", "\n", "# 原始矩阵 A\n", "A = np.array([[1, 2], [4, 8], [7, 10]])\n", "\n", "# 计算均值中心化\n", "A_mean = np.mean(A, axis=0)  # 每列的均值\n", "A_centered = A - A_mean      # 减去均值实现中心化\n", "\n", "# 使用已计算的右奇异向量矩阵 V\n", "V = np.array([[0.52901149, -0.84861466], [0.84861466, 0.52901149]])\n", "\n", "# 选择第一主成分方向进行投影（第一列）\n", "V_pca = V[:, 0]\n", "\n", "# 投影均值中心化的数据到主成分方向（填空）\n", "X_pca = pass\n", "\n", "# 可视化 PCA 结果\n", "plt.figure(figsize=(8, 6))\n", "plt.scatter(X_pca, np.zeros_like(X_pca), color='blue', label='投影点')\n", "plt.axhline(0, color='black', linewidth=0.5)\n", "plt.axvline(0, color='black', linewidth=0.5)\n", "plt.grid(True, linestyle='--', alpha=0.6)\n", "plt.title('PCA 投影到主成分 1')\n", "plt.xlabel('主成分 1')\n", "plt.ylabel('零轴')\n", "plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "25e6da95", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}