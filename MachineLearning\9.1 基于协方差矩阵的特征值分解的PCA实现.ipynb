{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ea6b7c9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中心化后的数据:\n", "[[-3.   -4.67]\n", " [ 0.    1.33]\n", " [ 3.    3.33]]\n", "\n", "协方差矩阵:\n", "[[ 9.   12.  ]\n", " [12.   17.33]]\n", "\n", "特征值:\n", "[ 0.46 25.87]\n", "\n", "特征向量:\n", "[[-0.81 -0.58]\n", " [ 0.58 -0.81]]\n", "\n", "投影后的数据:\n", "[[-0.26  5.54]\n", " [ 0.77 -1.09]\n", " [-0.51 -4.46]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 原始数据集\n", "X = np.array([[1, 2],\n", "              [4, 8],\n", "              [7, 10]])\n", "\n", "# 1. 数据标准化（中心化）\n", "# 列方向计算均值axis=0（填空）\n", "mean = np.mean(X, axis=0)\n", "# 每个数据点减去均值（填空）\n", "X_centered = X - mean\n", "\n", "# 2. 计算协方差矩阵\n", "#根据行数，获取样本数量（填空）\n", "n_samples = X.shape[0]\n", "# 使用numpy.cov计算中心化后数据的协方差矩阵,注意要转置，因为np.cov默认行代表变量(填空)\n", "cov_matrix = np.cov(X_centered.T)\n", "\n", "# 3. 计算协方差矩阵的特征值和特征向量（填空）\n", "eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)\n", "# 4. 数据投影到特征向量方向上\n", "# 将中心化后的数据矩阵乘以特征向量矩阵，完成投影（填空）\n", "Y = X_centered @ eigenvectors\n", "\n", "# 保留两位小数\n", "X_centered = np.round(X_centered, 2)\n", "cov_matrix = np.round(cov_matrix, 2)\n", "eigenvalues = np.round(eigenvalues, 2)\n", "eigenvectors = np.round(eigenvectors, 2)\n", "Y = np.round(Y, 2)\n", "\n", "# 输出结果\n", "print(\"中心化后的数据:\")\n", "print(X_centered)\n", "print(\"\\n协方差矩阵:\")\n", "print(cov_matrix)\n", "print(\"\\n特征值:\")\n", "print(eigenvalues)\n", "print(\"\\n特征向量:\")\n", "print(eigenvectors)\n", "print(\"\\n投影后的数据:\")\n", "print(Y)\n", "\n", "# 5. 可视化\n", "plt.figure(figsize=(10, 8))\n", "plt.scatter(X_centered[:, 0], X_centered[:, 1], color='red', label='Centered Data')\n", "plt.scatter(Y[:, 0], np.zeros(Y.shape[0]), color='blue', label='Projected Data on PC1')\n", "plt.scatter(np.zeros(Y.shape[0]), Y[:, 1], color='green', label='Projected Data on PC2')\n", "\n", "# 绘制特征向量（主成分）\n", "origin = np.zeros(2)\n", "plt.quiver(*origin, *eigenvectors[:, 0], color='blue', scale=3, label='First Principal Component')\n", "plt.quiver(*origin, *eigenvectors[:, 1], color='green', scale=3, label='Second Principal Component')\n", "\n", "# 添加坐标轴和标题\n", "plt.axhline(0, color='black', linewidth=0.5)\n", "plt.axvline(0, color='black', linewidth=0.5)\n", "plt.xlabel('PC1')\n", "plt.ylabel('PC2')\n", "plt.legend()\n", "plt.title('PCA Projection Visualization')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "30de3c91", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}