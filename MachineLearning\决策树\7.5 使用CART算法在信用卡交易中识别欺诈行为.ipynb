{"cells": [{"cell_type": "code", "id": "c3530619", "metadata": {"ExecuteTime": {"end_time": "2025-05-16T01:26:59.677299Z", "start_time": "2025-05-16T01:26:45.417392Z"}}, "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import confusion_matrix, accuracy_score, precision_score, recall_score, roc_auc_score, roc_curve\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体\n", "plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题\n", "\n", "# 加载数据\n", "data = pd.read_csv('data/creditcard.csv')\n", "\n", "# 数据预处理\n", "X = data.drop('Class', axis=1)\n", "y = data['Class']\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)\n", "\n", "# 训练CART模型\n", "clf = DecisionTreeClassifier(random_state=42)\n", "clf.fit(X_train, y_train)\n", "\n", "# 预测\n", "y_pred = clf.predict(X_test)\n", "y_pred_prob = clf.predict_proba(X_test)[:, 1]\n", "\n", "# 评估性能指标\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "accuracy = accuracy_score(y_test, y_pred)\n", "precision = precision_score(y_test, y_pred)\n", "recall = recall_score(y_test, y_pred)\n", "roc_auc = roc_auc_score(y_test, y_pred_prob)\n", "\n", "# 打印性能指标\n", "print(\"混淆矩阵:\")\n", "print(conf_matrix)\n", "print(f\"准确率: {accuracy:.4f}\")\n", "print(f\"精确率: {precision:.4f}\")\n", "print(f\"召回率: {recall:.4f}\")\n", "print(f\"AUC: {roc_auc:.4f}\")\n", "\n", "# 可视化混淆矩阵\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', xticklabels=['预测为非欺诈', '预测为欺诈'], yticklabels=['实际为非欺诈', '实际为欺诈'])\n", "plt.xlabel('预测标签')\n", "plt.ylabel('实际标签')\n", "plt.title('混淆矩阵')\n", "plt.show()\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["混淆矩阵:\n", "[[85264    31]\n", " [   39   109]]\n", "准确率: 0.9992\n", "精确率: 0.7786\n", "召回率: 0.7365\n", "AUC: 0.8681\n"]}, {"data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 2}, {"cell_type": "code", "execution_count": null, "id": "e4364d6e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}