from sklearn.naive_bayes import GaussianNB
from sklearn.datasets import load_digits
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

#加载手写数字识别的数据集
digits=pass

#提取特征，从digits提取data数据（填空）
x=pass

#标签0-9，从digits提取target数据（填空）
y=pass

#将数据集分割为训练集和测试集30%，随机种子数为420（填空）
x_train,x_test,y_train,y_test=pass

#初始化并训练高斯朴素贝叶斯分类器（填空）
gnb=pass

#计算模型在测试集上的准确性（填空）
score=pass
print("测试集的准确性：",score)

#使用模型对测试集进行预测（填空）
ypred=pass

print(ypred)

#使用confusion_matrix函数，计算混淆矩阵(真实的测试的标签，模型预测出来的标签）（填空）
conf_matrix=pass
print(conf_matrix)

#可视化混淆矩阵
plt.figure(figsize=(10,7))
sns.heatmap(conf_matrix,annot=True,fmt='d',cmap='Blues')
plt.title('Confusion Matrix')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.show()




