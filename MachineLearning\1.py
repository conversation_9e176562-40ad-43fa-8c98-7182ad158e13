from manim import *

class SVDDecomposition(Scene):
    def construct(self):
        # 原始单位圆
        unit_circle = Circle(radius=1, color=BLUE)
        unit_circle_label = Text("单位圆", font_size=24).next_to(unit_circle, UP)
        self.play(Create(unit_circle), Write(unit_circle_label))
        self.wait(1)

        # 定义矩阵 A
        A = [[3, 1], [1, 3]]
        matrix_a = Matrix(A)
        matrix_label = Text("A = ", font_size=32).next_to(matrix_a, LEFT)
        matrix_group = VGroup(matrix_label, matrix_a).to_corner(UL)
        self.play(Write(matrix_group))
        self.wait(1)

        # Step 1: 应用 V^T
        vt_matrix = [[-0.7071, -0.7071], [-0.7071, 0.7071]]
        vt_applied = unit_circle.copy().apply_matrix(vt_matrix)
        vt_label = Text("应用 $V^T$", font_size=24).next_to(vt_applied, DOWN)

        self.play(Transform(unit_circle, vt_applied), FadeIn(vt_label))
        self.wait(1)

        # Step 2: 应用 Σ
        sigma_matrix = [[4, 0], [0, 2]]
        sigma_applied = unit_circle.copy().apply_matrix(sigma_matrix)
        sigma_label = Text("应用 $\\Sigma$", font_size=24).next_to(sigma_applied, DOWN)

        self.play(Transform(unit_circle, sigma_applied), FadeOut(vt_label), FadeIn(sigma_label))
        self.wait(1)

        # Step 3: 应用 U
        u_matrix = [[-0.7071, -0.7071], [-0.7071, 0.7071]]
        u_applied = unit_circle.copy().apply_matrix(u_matrix)
        u_label = Text("应用 $U$", font_size=24).next_to(u_applied, DOWN)

        self.play(Transform(unit_circle, u_applied), FadeOut(sigma_label), FadeIn(u_label))
        self.wait(1)

        # 最终效果标注
        result_label = Text("最终效果：A x 单位圆", font_size=28).to_edge(DOWN)
        self.play(FadeIn(result_label))
        self.wait(2)
