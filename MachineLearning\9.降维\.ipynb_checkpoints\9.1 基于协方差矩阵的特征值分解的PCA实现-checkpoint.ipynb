{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ea6b7c9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中心化后的数据:\n", "[[-3.   -4.67]\n", " [ 0.    1.33]\n", " [ 3.    3.33]]\n", "\n", "协方差矩阵:\n", "[[ 9.   12.  ]\n", " [12.   17.33]]\n", "\n", "特征值:\n", "[ 0.46 25.87]\n", "\n", "特征向量:\n", "[[-0.81 -0.58]\n", " [ 0.58 -0.81]]\n", "\n", "投影后的数据:\n", "[[-0.26  5.54]\n", " [ 0.77 -1.09]\n", " [-0.51 -4.46]]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 原始数据集\n", "X = np.array([[1, 2],\n", "              [4, 8],\n", "              [7, 10]])\n", "\n", "# 1. 数据标准化（中心化）\n", "# 列方向计算均值axis=0（填空）\n", "mean = pass\n", "# 每个数据点减去均值（填空）\n", "X_centered = pass\n", "\n", "# 2. 计算协方差矩阵\n", "#根据行数，获取样本数量（填空）\n", "n_samples = pass\n", "# 使用numpy.cov计算中心化后数据的协方差矩阵,注意要转置，因为np.cov默认行代表变量(填空)\n", "cov_matrix = pass\n", "\n", "# 3. 计算协方差矩阵的特征值和特征向量（填空）\n", "eigenvalues, eigenvectors = np.linalg.eig(pass)\n", "\n", "# 4. 数据投影到特征向量方向上\n", "# 将中心化后的数据矩阵乘以特征向量矩阵，完成投影（填空）\n", "Y = pass\n", "\n", "# 保留两位小数\n", "X_centered = np.round(X_centered, 2)\n", "cov_matrix = np.round(cov_matrix, 2)\n", "eigenvalues = np.round(eigenvalues, 2)\n", "eigenvectors = np.round(eigenvectors, 2)\n", "Y = np.round(Y, 2)\n", "\n", "# 输出结果\n", "print(\"中心化后的数据:\")\n", "print(X_centered)\n", "print(\"\\n协方差矩阵:\")\n", "print(cov_matrix)\n", "print(\"\\n特征值:\")\n", "print(eigenvalues)\n", "print(\"\\n特征向量:\")\n", "print(eigenvectors)\n", "print(\"\\n投影后的数据:\")\n", "print(Y)\n", "\n", "# 5. 可视化\n", "plt.figure(figsize=(10, 8))\n", "plt.scatter(X_centered[:, 0], X_centered[:, 1], color='red', label='Centered Data')\n", "plt.scatter(Y[:, 0], np.zeros(Y.shape[0]), color='blue', label='Projected Data on PC1')\n", "plt.scatter(np.zeros(Y.shape[0]), Y[:, 1], color='green', label='Projected Data on PC2')\n", "\n", "# 绘制特征向量（主成分）\n", "origin = np.zeros(2)\n", "plt.quiver(*origin, *eigenvectors[:, 0], color='blue', scale=3, label='First Principal Component')\n", "plt.quiver(*origin, *eigenvectors[:, 1], color='green', scale=3, label='Second Principal Component')\n", "\n", "# 添加坐标轴和标题\n", "plt.axhline(0, color='black', linewidth=0.5)\n", "plt.axvline(0, color='black', linewidth=0.5)\n", "plt.xlabel('PC1')\n", "plt.ylabel('PC2')\n", "plt.legend()\n", "plt.title('PCA Projection Visualization')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "30de3c91", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}