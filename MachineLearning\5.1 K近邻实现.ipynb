{"cells": [{"cell_type": "markdown", "id": "3e149daf", "metadata": {}, "source": ["# 1.K近邻实现-python搭建"]}, {"cell_type": "code", "execution_count": 1, "id": "0349ba7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['A']\n"]}], "source": ["import numpy as np\n", "from collections import Counter\n", "\n", "# 样本数据\n", "X_train = np.array([\n", "    [1, 2.1],  # 类A\n", "    [2.2, 1],  # 类A\n", "    [4, 5],    # 类B\n", "    [5, 3.8]   # 类B\n", "])\n", "\n", "#样本分类标签\n", "y_train=np.array(['A','A','B','B'])\n", "\n", "#新样本点\n", "X_test=np.array([[3,3]])\n", "\n", "#计算每个点的距离np.linalg.norm（欧氏距离）X_train ,X_test \n", "#axis=1 表示沿着行的方向（填空）\n", "def calculate_distances(X_train, X_test):\n", "    # 计算测试点到所有训练点的欧氏距离\n", "    distances = np.linalg.norm(X_train - X_test, axis=1)\n", "    return distances\n", "\n", "#K近邻算法的实现\n", "def knn_predict(X_train, y_train, X_test, k=3):\n", "    # 最终分类的结果\n", "    predictions = []\n", "    for x in X_test:\n", "        # 计算测试点到训练点的距离\n", "        distances = calculate_distances(X_train, x)\n", "        # 找到距离最近的K个点的索引\n", "        k_indices = distances.argsort()[:k]\n", "        # 找到最近K个点所对应的标签\n", "        k_nearest_labels = y_train[k_indices]\n", "        # 统计出现次数最多的标签\n", "        most_common = Counter(k_nearest_labels).most_common(1)\n", "        # 取出预测结果\n", "        predictions.append(most_common[0][0])\n", "    return predictions\n", "\n", "\n", "#使用K近邻算法进行预测\n", "predictions=knn_predict(X_train,y_train,X_test,k=3)\n", "print(predictions)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "1465b045", "metadata": {}, "source": ["# 2.K近邻实现-sklearn"]}, {"cell_type": "code", "execution_count": 2, "id": "8d79732b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['A']\n"]}], "source": ["import numpy as np\n", "from sklearn.neighbors import KNeighborsClassifier\n", "\n", "# 修改后的样本数据\n", "X_train = np.array([\n", "    [1, 2.1],  # 类A\n", "    [2.2, 1],  # 类A\n", "    [4, 5],    # 类B\n", "    [5, 3.8]   # 类B\n", "])\n", "y_train = np.array(['A', 'A', 'B', 'B'])\n", "\n", "# 新样本点\n", "X_test = np.array([[3, 3]])\n", "\n", "knn=KNeighborsClassifier(n_neighbors=3)\n", "\n", "knn.fit(X_train,y_train)\n", "\n", "predictions=knn.predict(X_test)\n", "\n", "print(predictions)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "c06bf84e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}