{"cells": [{"cell_type": "markdown", "id": "c7440bef", "metadata": {}, "source": ["# 凝聚型聚类python"]}, {"cell_type": "code", "id": "521f0ede", "metadata": {"ExecuteTime": {"end_time": "2025-06-09T03:04:01.919248Z", "start_time": "2025-06-09T03:04:01.911149Z"}}, "source": ["def euclidean_distance(point1, point2):\n", "    # 计算两点间的欧氏距离\n", "    return sum((p1 - p2) ** 2 for p1, p2 in zip(point1, point2)) ** 0.5\n", "\n", "def closest_clusters(clusters):\n", "    # 找到最接近的两个簇\n", "    min_distance = float('inf')  # 初始化最小距离为无穷大\n", "    closest_pair = (None, None)   # 初始化最近的簇对\n", "\n", "    # 遍历所有簇对\n", "    for i in range(len(clusters)):\n", "        for j in range(i + 1, len(clusters)):\n", "            # 计算两个簇的欧氏距离\n", "            distance = euclidean_distance(clusters[i]['centroid'], clusters[j]['centroid'])\n", "            if distance < min_distance:  # 如果当前距离小于最小距离\n", "                min_distance = distance  # 更新最小距离\n", "                closest_pair = (i, j)    # 更新最近的簇对\n", "\n", "    return closest_pair  # 返回最近的簇对索引\n", "\n", "def merge_clusters(clusters, pair):\n", "    # 合并两个簇\n", "    i, j = pair  # 获取要合并的簇索引\n", "    # 创建新的簇，包括所有点和新的中心点\n", "    new_cluster = {\n", "        'points': clusters[i]['points'] + clusters[j]['points'],  # 合并点\n", "        'centroid': tuple(sum(x) / len(x) for x in zip(clusters[i]['centroid'], clusters[j]['centroid']))  # 更新中心点\n", "    }\n", "    # 删除原有簇并添加新簇\n", "    del clusters[j], clusters[i]  # 删除被合并的簇\n", "    clusters.append(new_cluster)   # 添加新簇\n", "\n", "# 初始化数据点\n", "data_points = {'A': (1, 2), 'B': (2, 3), 'C': (3, 3), 'D': (6, 7)}\n", "\n", "# 初始化簇\n", "clusters = [{'points': [key], 'centroid': value} for key, value in data_points.items()]\n", "\n", "# 凝聚型聚类过程\n", "while len(clusters) > 1:\n", "    i, j = closest_clusters(clusters)  # 找到最近的簇对\n", "    merge_clusters(clusters, (i, j))    # 合并这两个簇\n", "    print(\"合并后的簇：\", [''.join(cluster['points']) for cluster in clusters])  # 输出当前簇状态\n", "\n", "# 最终结果\n", "final_cluster = ''.join(clusters[0]['points'])  # 获取最终聚类结果\n", "print(\"最终聚类结果：\", final_cluster)  # 输出最终合并后的簇\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并后的簇： ['A', 'D', 'BC']\n", "合并后的簇： ['D', 'ABC']\n", "合并后的簇： ['DABC']\n", "最终聚类结果： DABC\n"]}], "execution_count": 3}, {"cell_type": "markdown", "id": "9249d7b8", "metadata": {}, "source": ["# 凝聚型聚类scipy"]}, {"cell_type": "code", "id": "2bf7ed0f", "metadata": {"ExecuteTime": {"end_time": "2025-06-09T03:04:11.738684Z", "start_time": "2025-06-09T03:04:11.586965Z"}}, "source": ["import matplotlib.pyplot as plt\n", "from scipy.cluster.hierarchy import dendrogram, linkage\n", "import numpy as np\n", "\n", "# 数据点\n", "data_points = np.array([[1, 2], [2, 3], [3, 3], [6, 7]])\n", "\n", "# 使用 linkage 函数进行凝聚型聚类\n", "# 使用 \"single\" 链接方法\n", "linked = linkage(data_points, 'single')\n", "\n", "# 绘制树状图\n", "plt.figure(figsize=(10, 7))\n", "dendrogram(linked,\n", "           orientation='top',\n", "           labels=['A', 'B', 'C', 'D'],  # 使用 ABCD 作为标签\n", "           distance_sort='descending',\n", "           show_leaf_counts=True)\n", "\n", "plt.title('Agglomerative Clustering Dendrogram with Custom Labels')\n", "plt.xlabel('Cluster Size')\n", "plt.ylabel('Distance')\n", "plt.show()"], "outputs": [{"data": {"text/plain": ["<Figure size 1000x700 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 4}, {"cell_type": "code", "execution_count": null, "id": "5184d370", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}